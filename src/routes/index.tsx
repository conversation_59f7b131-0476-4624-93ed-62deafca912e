import { createFileRoute } from "@tanstack/react-router"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import toast from "react-hot-toast"
import CaseInvestigationTable from "../components/CaseInvestigationTable"
import CorruptionDetectionSummary from "../components/CorruptionDetectionSummary"
import QuickStatsCards from "../components/QuickStatsCards"
import RiskOverviewPanel from "../components/RiskOverviewPanel"

import CaseAnalysisPanel from "../components/CaseAnalysisPanel"
import ToastProvider from "../components/ToastProvider"
import { CaseProvider } from "../contexts/CaseContext"
import { corruptionSummary, dashboardStats, mockCases } from "../data/mockData"
import type { ProcurementCase } from "../types/procurement"

export const Route = createFileRoute("/")({
	component: Dashboard,
})

function Dashboard() {
	const [selectedCase, setSelectedCase] = useState<ProcurementCase | null>(null)
	const [isLoading, setIsLoading] = useState(true)
	
	// Calculate overall risk score based on current cases
	const overallRiskScore = Math.round(
		mockCases.reduce((sum, case_) => sum + case_.riskScore, 0) / mockCases.length
	)

	// Demo loading and welcome toast
	useEffect(() => {
		const timer = setTimeout(() => {
			setIsLoading(false)
			toast.success("🎯 Procurement Analysis Dashboard Loaded", {
				duration: 3000,
			})
		}, 1500)

		// Welcome sequence
		const welcomeTimer = setTimeout(() => {
			toast("🔍 Scanning procurement data for corruption patterns...", {
				icon: "⚡",
				duration: 2000,
			})
		}, 2000)

		const alertTimer = setTimeout(() => {
			if (overallRiskScore >= 71) {
				toast.error("🚨 High Risk Cases Detected - Immediate Review Recommended", {
					duration: 4000,
				})
			}
		}, 4000)

		return () => {
			clearTimeout(timer)
			clearTimeout(welcomeTimer)
			clearTimeout(alertTimer)
		}
	}, [overallRiskScore])

	const handleCaseSelect = (caseItem: ProcurementCase) => {
		setSelectedCase(caseItem)
		toast.loading("📊 Loading detailed case analysis...", {
			duration: 1000,
		})

		setTimeout(() => {
			if (caseItem.riskScore >= 71) {
				toast.error(`🚨 High Risk Case: ${caseItem.id}`, {
					duration: 3000,
				})
			} else if (caseItem.riskScore >= 31) {
				toast("⚠️ Medium Risk Case - Review Recommended", {
					icon: "📋",
					duration: 2500,
				})
			} else {
				toast.success("✅ Low Risk Case - Standard Oversight", {
					duration: 2000,
				})
			}
		}, 1000)
	}

	const handleCaseSelectById = (caseId: string) => {
		const caseItem = mockCases.find(c => c.id === caseId)
		if (caseItem) {
			handleCaseSelect(caseItem)
		} else {
			toast.error(`Case ${caseId} not found`, { duration: 2000 })
		}
	}

	if (isLoading) {
		return (
			<div className="min-h-screen bg-gray-100 flex items-center justify-center">
				<motion.div 
					className="text-center"
					initial={{ opacity: 0, scale: 0.8 }}
					animate={{ opacity: 1, scale: 1 }}
				>
					<div className="text-6xl mb-4">🔍</div>
					<h1 className="text-2xl font-bold text-gray-900 mb-2">
						Initializing Corruption Analysis
					</h1>
					<p className="text-gray-600">
						Loading procurement data and risk models...
					</p>
					<motion.div
						className="mt-6 w-64 h-2 bg-gray-200 rounded-full overflow-hidden mx-auto"
						initial={{ width: 0 }}
						animate={{ width: "16rem" }}
						transition={{ duration: 0.5 }}
					>
						<motion.div
							className="h-full bg-blue-600 rounded-full"
							initial={{ width: "0%" }}
							animate={{ width: "100%" }}
							transition={{ duration: 1.5, ease: "easeOut" }}
						/>
					</motion.div>
				</motion.div>
				<ToastProvider />
			</div>
		)
	}

	return (
		<CaseProvider onCaseSelect={handleCaseSelectById}>
			<div className="min-h-screen bg-gray-100">
				<ToastProvider />
				<motion.div
					className="max-w-7xl mx-auto px-4 py-6"
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
				>
				{/* Quick Stats */}
				<motion.div 
					className="mb-8"
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.1, duration: 0.6 }}
				>
					<QuickStatsCards stats={dashboardStats} />
				</motion.div>

				{/* Main Dashboard Grid */}
				<motion.div 
					className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8"
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.2, duration: 0.6 }}
				>
					{/* Risk Overview - Takes 1 column */}
					<div className="lg:col-span-1">
						<RiskOverviewPanel overallRiskScore={overallRiskScore} />
					</div>
					
					{/* Corruption Detection Summary - Takes 2 columns */}
					<div className="lg:col-span-2">
						<CorruptionDetectionSummary summary={corruptionSummary} />
					</div>
				</motion.div>



				{/* Case Investigation Table */}
				<motion.div
					className="mb-8"
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.3, duration: 0.6 }}
				>
					<CaseInvestigationTable 
						cases={mockCases} 
						onCaseSelect={handleCaseSelect}
					/>
				</motion.div>

				{/* Selected Case Details Panel */}
				{selectedCase && (
					<CaseAnalysisPanel 
						caseItem={selectedCase} 
						onClose={() => {
							setSelectedCase(null)
							toast("📋 Case analysis closed", {
								icon: "👋",
								duration: 1500,
							})
						}} 
					/>
				)}
			</motion.div>
		</div>
	</CaseProvider>
)
}
