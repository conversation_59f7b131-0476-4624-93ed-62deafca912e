export interface ProcurementCase {
	id: string
	projectName: string
	riskScore: number
	topViolations: ViolationType[]
	bidders: number
	winner: string
	amount: number
	status: CaseStatus
	agency: string
	datePosted: string
	deadline: string
	category: string
}

export interface ViolationType {
	type: CorruptionType
	severity: SeverityLevel
	description: string
	count?: number
}

export type CorruptionType =
	| "Company Collusion"
	| "Price Manipulation"
	| "Specification Rigging"
	| "Document Similarity"
	| "Zombie Companies"
	| "Repeated Winners"

export type SeverityLevel = "Low" | "Medium" | "High" | "Critical"

export type CaseStatus = "Under Review" | "Cleared" | "Pending" | "Flagged" | "Archived"

export type RiskLevel = "Low" | "Medium" | "High"

export interface DashboardStats {
	totalCases: number
	cleanCases: number
	flaggedCases: number
	underReview: number
	cleanPercentage: number
	flaggedPercentage: number
	reviewPercentage: number
}

export interface CorruptionSummary {
	violationCounts: Record<CorruptionType, number>
	severityDistribution: Record<SeverityLevel, number>
}

export interface CompanyRelationship {
	companyA: string
	companyB: string
	relationshipType: "Shared Directors" | "Shared Address" | "Shared Contact" | "Financial Link"
	strength: number
}

export interface PriceAnalysis {
	marketRate: number
	proposedPrice: number
	deviation: number
	historicalPrices: Array<{ date: string; price: number }>
}

export interface Alert {
	id: string
	type: "Critical" | "High Priority" | "Trend"
	title: string
	description: string
	caseId?: string
	timestamp: string
}