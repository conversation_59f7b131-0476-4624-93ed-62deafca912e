import type { RiskLevel, SeverityLevel } from "../types/procurement"

export function getRiskLevel(score: number): RiskLevel {
	if (score >= 71) return "High"
	if (score >= 31) return "Medium"
	return "Low"
}

export function getRiskColor(score: number): string {
	const level = getRiskLevel(score)
	switch (level) {
		case "High":
			return "text-red-600 bg-red-50 border-red-200"
		case "Medium":
			return "text-yellow-600 bg-yellow-50 border-yellow-200"
		case "Low":
			return "text-green-600 bg-green-50 border-green-200"
		default:
			return "text-gray-600 bg-gray-50 border-gray-200"
	}
}

export function getRiskIcon(score: number): string {
	const level = getRiskLevel(score)
	switch (level) {
		case "High":
			return "🚨"
		case "Medium":
			return "⚠️"
		case "Low":
			return "✅"
		default:
			return "ℹ️"
	}
}

export function getSeverityColor(severity: SeverityLevel): string {
	switch (severity) {
		case "Critical":
			return "text-red-700 bg-red-100"
		case "High":
			return "text-orange-700 bg-orange-100"
		case "Medium":
			return "text-yellow-700 bg-yellow-100"
		case "Low":
			return "text-green-700 bg-green-100"
		default:
			return "text-gray-700 bg-gray-100"
	}
}

export function formatCurrency(amount: number): string {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	}).format(amount)
}

export function formatPercentage(value: number): string {
	return `${value.toFixed(1)}%`
}

export function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleDateString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
	})
}