import { createContext, useContext, type ReactNode } from "react"

interface CaseContextType {
	onCaseSelect?: (caseId: string) => void
}

const CaseContext = createContext<CaseContextType>({})

export const useCaseContext = () => useContext(CaseContext)

interface CaseProviderProps {
	children: ReactNode
	onCaseSelect?: (caseId: string) => void
}

export function CaseProvider({ children, onCaseSelect }: CaseProviderProps) {
	return (
		<CaseContext.Provider value={{ onCaseSelect }}>
			{children}
		</CaseContext.Provider>
	)
}
