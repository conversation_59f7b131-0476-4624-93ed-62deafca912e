import { useState } from "react"
import toast from "react-hot-toast"
import type { Alert } from "../types/procurement"
import { formatDate } from "../utils/riskUtils"

interface AlertSystemProps {
	alerts: Alert[]
}

export default function AlertSystem({ alerts }: AlertSystemProps) {
	const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set())
	
	const visibleAlerts = alerts.filter(alert => !dismissedAlerts.has(alert.id))
	
	const handleViewCase = (caseId?: string) => {
		if (!caseId) return
		toast.success(`🔍 Opening case ${caseId}...`, { duration: 2000 })
		// In a real app, this would navigate to the case details
		console.log("Navigate to case:", caseId)
	}
	
	const handleDismissAlert = (alertId: string) => {
		setDismissedAlerts(prev => new Set([...prev, alertId]))
		toast.success("✅ Alert dismissed", { duration: 1500 })
	}

	const getAlertIcon = (type: <PERSON>ert["type"]) => {
		switch (type) {
			case "Critical": return "🚨"
			case "High Priority": return "⚠️"
			case "Trend": return "📊"
			default: return "ℹ️"
		}
	}

	const getAlertColor = (type: Alert["type"]) => {
		switch (type) {
			case "Critical": return "border-red-200 bg-red-50 text-red-800"
			case "High Priority": return "border-orange-200 bg-orange-50 text-orange-800"
			case "Trend": return "border-blue-200 bg-blue-50 text-blue-800"
			default: return "border-gray-200 bg-gray-50 text-gray-800"
		}
	}

	return (
		<div className="bg-white rounded-lg shadow-md p-6">
			<div className="flex items-center justify-between mb-6">
				<h2 className="text-xl font-bold text-gray-900">Priority Alerts</h2>
				<div className="text-sm text-gray-500">
					{visibleAlerts.length} active alerts
				</div>
			</div>
			
			<div className="space-y-4">
				{visibleAlerts.map((alert) => (
					<div
						key={alert.id}
						className={`border rounded-lg p-4 ${getAlertColor(alert.type)}`}
					>
						<div className="flex items-start justify-between">
							<div className="flex items-start space-x-3">
								<div className="text-2xl">
									{getAlertIcon(alert.type)}
								</div>
								<div className="flex-1">
									<div className="flex items-center space-x-2">
										<span className="font-semibold text-sm uppercase tracking-wide">
											{alert.type}
										</span>
										{alert.caseId && (
											<span className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
												{alert.caseId}
											</span>
										)}
									</div>
									<h3 className="font-medium text-base mt-1">
										{alert.title}
									</h3>
									<p className="text-sm mt-1 opacity-90">
										{alert.description}
									</p>
									<div className="text-xs mt-2 opacity-75">
										{formatDate(alert.timestamp)}
									</div>
								</div>
							</div>
							<div className="flex space-x-2">
								{alert.caseId && (
									<button 
										type="button"
										onClick={() => handleViewCase(alert.caseId)}
										className="text-xs px-3 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded transition-colors"
									>
										View Case
									</button>
								)}
								<button 
									type="button"
									onClick={() => handleDismissAlert(alert.id)}
									className="text-xs px-3 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded transition-colors"
								>
									Dismiss
								</button>
							</div>
						</div>
					</div>
				))}
			</div>

			{alerts.length === 0 && (
				<div className="text-center py-8 text-gray-500">
					<div className="text-4xl mb-2">✅</div>
					<p>No active alerts</p>
				</div>
			)}
		</div>
	)
}