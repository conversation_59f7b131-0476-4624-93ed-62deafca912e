import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Legend } from "recharts"
import { <PERSON><PERSON><PERSON><PERSON>gle, TrendingUp, Shield, Eye } from "lucide-react"
import type { CorruptionSummary, CorruptionType, SeverityLevel } from "../types/procurement"

interface CorruptionDetectionSummaryProps {
	summary: CorruptionSummary
}

export default function CorruptionDetectionSummary({ summary }: CorruptionDetectionSummaryProps) {
	const [activeChart, setActiveChart] = useState<"bar" | "pie">("bar")
	const [hoveredSegment, setHoveredSegment] = useState<string | null>(null)

	const violationEntries = Object.entries(summary.violationCounts) as [CorruptionType, number][]
	const severityEntries = Object.entries(summary.severityDistribution) as [SeverityLevel, number][]
	const totalSeverity = Object.values(summary.severityDistribution).reduce((sum, count) => sum + count, 0)

	// Data for Recharts
	const violationData = violationEntries.map(([type, count]) => ({
		name: type.replace(" ", "\n"),
		value: count,
		fullName: type,
		color: getViolationColor(type),
		riskLevel: getRiskLevel(type)
	}))

	const severityData = severityEntries.map(([severity, count]) => ({
		name: severity,
		value: count,
		percentage: ((count / totalSeverity) * 100).toFixed(1),
		color: getSeverityColor(severity),
		icon: getSeverityIcon(severity)
	}))

	function getViolationColor(type: CorruptionType) {
		const colors = {
			"Company Collusion": "#dc2626",
			"Price Manipulation": "#ea580c", 
			"Specification Rigging": "#d97706",
			"Document Similarity": "#ca8a04",
			"Zombie Companies": "#65a30d",
			"Repeated Winners": "#059669"
		}
		return colors[type] || "#6b7280"
	}

	function getRiskLevel(type: CorruptionType) {
		const highRisk = ["Company Collusion", "Price Manipulation", "Zombie Companies"]
		const mediumRisk = ["Specification Rigging", "Document Similarity"]
		return highRisk.includes(type) ? "High" : mediumRisk.includes(type) ? "Medium" : "Low"
	}

	function getSeverityColor(severity: SeverityLevel) {
		const colors = {
			Critical: "#dc2626",
			High: "#ea580c", 
			Medium: "#d97706",
			Low: "#16a34a"
		}
		return colors[severity] || "#6b7280"
	}

	function getSeverityIcon(severity: SeverityLevel) {
		const icons = {
			Critical: "🔴",
			High: "🟠", 
			Medium: "🟡",
			Low: "🟢"
		}
		return icons[severity] || "⚫"
	}

	const CustomTooltip = ({ active, payload }: any) => {
		if (active && payload && payload.length) {
			const data = payload[0].payload
			return (
				<motion.div 
					initial={{ opacity: 0, scale: 0.8 }}
					animate={{ opacity: 1, scale: 1 }}
					className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg"
				>
					<p className="font-semibold text-gray-900">{data.fullName || data.name}</p>
					<p className="text-sm text-gray-600">Cases: {data.value}</p>
					{data.riskLevel && (
						<p className="text-sm">
							Risk Level: <span className={`font-medium ${
								data.riskLevel === "High" ? "text-red-600" : 
								data.riskLevel === "Medium" ? "text-orange-600" : "text-green-600"
							}`}>{data.riskLevel}</span>
						</p>
					)}
					{data.percentage && (
						<p className="text-sm text-gray-600">Percentage: {data.percentage}%</p>
					)}
				</motion.div>
			)
		}
		return null
	}

	const criticalCount = severityData.find(d => d.name === "Critical")?.value || 0
	const highCount = severityData.find(d => d.name === "High")?.value || 0

	return (
		<motion.div 
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="bg-white rounded-lg shadow-lg p-6 border border-gray-100"
		>
			{/* Header with Key Metrics */}
			<div className="flex items-center justify-between mb-6">
				<div>
					<h2 className="text-xl font-bold text-gray-900 mb-2">🔍 Corruption Detection Summary</h2>
					<div className="flex items-center space-x-6 text-sm">
						<div className="flex items-center space-x-2">
							<AlertTriangle className="w-4 h-4 text-red-600" />
							<span className="text-gray-600">Critical Issues: <span className="font-semibold text-red-600">{criticalCount}</span></span>
						</div>
						<div className="flex items-center space-x-2">
							<TrendingUp className="w-4 h-4 text-orange-600" />
							<span className="text-gray-600">High Risk: <span className="font-semibold text-orange-600">{highCount}</span></span>
						</div>
						<div className="flex items-center space-x-2">
							<Eye className="w-4 h-4 text-blue-600" />
							<span className="text-gray-600">Total Cases: <span className="font-semibold text-blue-600">{totalSeverity}</span></span>
						</div>
					</div>
				</div>
				
				{/* Chart Toggle */}
				<div className="flex bg-gray-100 rounded-lg p-1">
					<button
						onClick={() => setActiveChart("bar")}
						className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
							activeChart === "bar" 
								? "bg-white text-blue-600 shadow-sm" 
								: "text-gray-600 hover:text-gray-900"
						}`}
					>
						📊 Violations
					</button>
					<button
						onClick={() => setActiveChart("pie")}
						className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
							activeChart === "pie" 
								? "bg-white text-blue-600 shadow-sm" 
								: "text-gray-600 hover:text-gray-900"
						}`}
					>
						🥧 Severity
					</button>
				</div>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
				{/* Main Chart Area */}
				<div className="lg:col-span-2">
					<motion.div
						key={activeChart}
						initial={{ opacity: 0, x: 20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.3 }}
						className="h-80"
					>
						{activeChart === "bar" ? (
							<div>
								<h3 className="text-lg font-semibold text-gray-800 mb-4">🚨 Violation Frequency Analysis</h3>
								<ResponsiveContainer width="100%" height={300}>
									<BarChart data={violationData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
										<CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
										<XAxis 
											dataKey="name" 
											tick={{ fontSize: 11, fill: "#6b7280" }}
											angle={-45}
											textAnchor="end"
											height={80}
										/>
										<YAxis tick={{ fontSize: 12, fill: "#6b7280" }} />
										<Tooltip content={<CustomTooltip />} />
										<Bar 
											dataKey="value" 
											radius={[4, 4, 0, 0]}
											fill="#3b82f6"
										>
											{violationData.map((entry, index) => (
												<Cell key={`cell-${index}`} fill={entry.color} />
											))}
										</Bar>
									</BarChart>
								</ResponsiveContainer>
							</div>
						) : (
							<div>
								<h3 className="text-lg font-semibold text-gray-800 mb-4">⚖️ Risk Severity Distribution</h3>
								<ResponsiveContainer width="100%" height={300}>
									<PieChart>
										<Pie
											data={severityData}
											cx="50%"
											cy="50%"
											innerRadius={60}
											outerRadius={120}
											paddingAngle={2}
											dataKey="value"
											onMouseEnter={(_, index) => setHoveredSegment(severityData[index].name)}
											onMouseLeave={() => setHoveredSegment(null)}
										>
											{severityData.map((entry, index) => (
												<Cell 
													key={`cell-${index}`} 
													fill={entry.color}
													stroke={hoveredSegment === entry.name ? "#ffffff" : "none"}
													strokeWidth={hoveredSegment === entry.name ? 3 : 0}
												/>
											))}
										</Pie>
										<Tooltip content={<CustomTooltip />} />
										<Legend 
											formatter={(value, entry: any) => (
												<span style={{ color: entry.color }}>
													{entry.payload.icon} {value} ({entry.payload.percentage}%)
												</span>
											)}
										/>
									</PieChart>
								</ResponsiveContainer>
							</div>
						)}
					</motion.div>
				</div>

				{/* Insights Panel */}
				<div className="lg:col-span-1">
					<h3 className="text-lg font-semibold text-gray-800 mb-4">🔍 Key Insights</h3>
					<div className="space-y-4">
						{/* Top Risk Alert */}
						<motion.div 
							initial={{ opacity: 0, scale: 0.9 }}
							animate={{ opacity: 1, scale: 1 }}
							className="bg-red-50 border border-red-200 rounded-lg p-4"
						>
							<div className="flex items-center space-x-2 mb-2">
								<AlertTriangle className="w-5 h-5 text-red-600" />
								<span className="font-semibold text-red-900">Highest Risk</span>
							</div>
							<p className="text-sm text-red-700">
								<strong>{violationData[0]?.fullName}</strong> detected in {violationData[0]?.value} cases
							</p>
							<div className="mt-2 text-xs text-red-600">
								Requires immediate investigation
							</div>
						</motion.div>

						{/* Trend Analysis */}
						<motion.div 
							initial={{ opacity: 0, scale: 0.9 }}
							animate={{ opacity: 1, scale: 1 }}
							transition={{ delay: 0.1 }}
							className="bg-orange-50 border border-orange-200 rounded-lg p-4"
						>
							<div className="flex items-center space-x-2 mb-2">
								<TrendingUp className="w-5 h-5 text-orange-600" />
								<span className="font-semibold text-orange-900">Trend Alert</span>
							</div>
							<p className="text-sm text-orange-700">
								{criticalCount + highCount} high-priority cases need review
							</p>
							<div className="mt-2 text-xs text-orange-600">
								{((criticalCount + highCount) / totalSeverity * 100).toFixed(1)}% of total cases
							</div>
						</motion.div>

						{/* Success Metric */}
						<motion.div 
							initial={{ opacity: 0, scale: 0.9 }}
							animate={{ opacity: 1, scale: 1 }}
							transition={{ delay: 0.2 }}
							className="bg-green-50 border border-green-200 rounded-lg p-4"
						>
							<div className="flex items-center space-x-2 mb-2">
								<Shield className="w-5 h-5 text-green-600" />
								<span className="font-semibold text-green-900">System Performance</span>
							</div>
							<p className="text-sm text-green-700">
								Detected {Object.values(summary.violationCounts).reduce((a, b) => a + b, 0)} potential issues
							</p>
							<div className="mt-2 text-xs text-green-600">
								Automated risk assessment active
							</div>
						</motion.div>
					</div>
				</div>
			</div>

			{/* Quick Actions */}
			<motion.div 
				initial={{ opacity: 0, y: 10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6 pt-6 border-t border-gray-200"
			>
				<div className="flex items-center justify-between">
					<div className="text-sm text-gray-600">
						Last updated: {new Date().toLocaleTimeString()}
					</div>
					<div className="flex space-x-3">
						<button 
							type="button"
							onClick={() => {
								// Simulate report generation
								const blob = new Blob([`Corruption Analysis Report\nGenerated: ${new Date().toLocaleString()}\n\nTotal Violations: ${Object.values(summary.violationCounts).reduce((a, b) => a + b, 0)}\nCritical Cases: ${summary.severityDistribution.Critical}\nHigh Risk Cases: ${summary.severityDistribution.High}`], 
									{ type: 'text/plain' })
								const url = URL.createObjectURL(blob)
								const a = document.createElement('a')
								a.href = url
								a.download = `corruption-report-${new Date().toISOString().split('T')[0]}.txt`
								a.click()
								URL.revokeObjectURL(url)
							}}
							className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
						>
							📊 Generate Report
						</button>
						<button 
							type="button"
							onClick={() => {
								// Simulate refresh
								window.location.reload()
							}}
							className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
						>
							🔄 Refresh Data
						</button>
					</div>
				</div>
			</motion.div>
		</motion.div>
	)
}