import { useState } from "react"
import type { ProcurementCase, CaseStatus } from "../types/procurement"
import { getRiskColor, getRiskIcon, formatCurrency } from "../utils/riskUtils"

interface CaseInvestigationTableProps {
	cases: ProcurementCase[]
	onCaseSelect?: (caseItem: ProcurementCase) => void
}

export default function CaseInvestigationTable({ cases, onCaseSelect }: CaseInvestigationTableProps) {
	const [sortField, setSortField] = useState<keyof ProcurementCase>("riskScore")
	const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
	const [filterStatus, setFilterStatus] = useState<CaseStatus | "All">("All")
	const [searchTerm, setSearchTerm] = useState("")

	const filteredCases = cases
		.filter(caseItem => {
			if (filterStatus !== "All" && caseItem.status !== filterStatus) return false
			if (searchTerm && !caseItem.projectName.toLowerCase().includes(searchTerm.toLowerCase()) &&
				!caseItem.winner.toLowerCase().includes(searchTerm.toLowerCase()) &&
				!caseItem.id.toLowerCase().includes(searchTerm.toLowerCase())) return false
			return true
		})
		.sort((a, b) => {
			const aValue = a[sortField]
			const bValue = b[sortField]
			if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
			if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
			return 0
		})

	const handleSort = (field: keyof ProcurementCase) => {
		if (sortField === field) {
			setSortDirection(sortDirection === "asc" ? "desc" : "asc")
		} else {
			setSortField(field)
			setSortDirection("desc")
		}
	}

	const getStatusColor = (status: CaseStatus) => {
		switch (status) {
			case "Under Review": return "bg-blue-100 text-blue-800"
			case "Cleared": return "bg-green-100 text-green-800"
			case "Pending": return "bg-yellow-100 text-yellow-800"
			case "Flagged": return "bg-red-100 text-red-800"
			case "Archived": return "bg-gray-100 text-gray-800"
			default: return "bg-gray-100 text-gray-800"
		}
	}

	const getStatusIcon = (status: CaseStatus) => {
		switch (status) {
			case "Under Review": return "🔍"
			case "Cleared": return "✅"
			case "Pending": return "⏳"
			case "Flagged": return "🚩"
			case "Archived": return "📁"
			default: return "📋"
		}
	}

	return (
		<div className="bg-white rounded-lg shadow-md p-6">
			<div className="flex justify-between items-center mb-6">
				<h2 className="text-xl font-bold text-gray-900">Case Investigation Table</h2>
				<div className="flex items-center space-x-4">
					<input
						type="text"
						placeholder="Search cases..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
					<select
						value={filterStatus}
						onChange={(e) => setFilterStatus(e.target.value as CaseStatus | "All")}
						className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<option value="All">All Status</option>
						<option value="Under Review">Under Review</option>
						<option value="Cleared">Cleared</option>
						<option value="Pending">Pending</option>
						<option value="Flagged">Flagged</option>
						<option value="Archived">Archived</option>
					</select>
				</div>
			</div>

			<div className="overflow-x-auto">
				<table className="min-w-full table-auto">
					<thead className="bg-gray-50">
						<tr>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("id")}
							>
								Tender ID {sortField === "id" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("projectName")}
							>
								Project Name {sortField === "projectName" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("riskScore")}
							>
								Risk Score {sortField === "riskScore" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Top Violations
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("bidders")}
							>
								Bidders {sortField === "bidders" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("winner")}
							>
								Winner {sortField === "winner" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("amount")}
							>
								Amount {sortField === "amount" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th 
								className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
								onClick={() => handleSort("status")}
							>
								Status {sortField === "status" && (sortDirection === "asc" ? "↑" : "↓")}
							</th>
							<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredCases.map((caseItem) => (
							<tr key={caseItem.id} className="hover:bg-gray-50">
								<td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
									{caseItem.id}
								</td>
								<td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
									{caseItem.projectName}
								</td>
								<td className="px-4 py-4 whitespace-nowrap">
									<div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium border ${getRiskColor(caseItem.riskScore)}`}>
										{getRiskIcon(caseItem.riskScore)} {caseItem.riskScore}
									</div>
								</td>
								<td className="px-4 py-4 text-sm text-gray-900">
									{caseItem.topViolations.length > 0 ? (
										<div className="space-y-1">
											{caseItem.topViolations.slice(0, 2).map((violation, index) => (
												<div key={index} className="text-xs text-red-600">
													{violation.type}
												</div>
											))}
											{caseItem.topViolations.length > 2 && (
												<div className="text-xs text-gray-500">
													+{caseItem.topViolations.length - 2} more
												</div>
											)}
										</div>
									) : (
										<span className="text-gray-500">None</span>
									)}
								</td>
								<td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
									{caseItem.bidders}
								</td>
								<td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
									{caseItem.winner}
								</td>
								<td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
									{formatCurrency(caseItem.amount)}
								</td>
								<td className="px-4 py-4 whitespace-nowrap">
									<span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(caseItem.status)}`}>
										{getStatusIcon(caseItem.status)} {caseItem.status}
									</span>
								</td>
								<td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
									<div className="flex space-x-2">
										<button 
											onClick={() => onCaseSelect?.(caseItem)}
											className="text-blue-600 hover:text-blue-900"
										>
											View
										</button>
										{caseItem.status !== "Cleared" && (
											<button className="text-red-600 hover:text-red-900">
												Flag
											</button>
										)}
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>

			<div className="mt-4 flex items-center justify-between">
				<div className="text-sm text-gray-700">
					Showing {filteredCases.length} of {cases.length} cases
				</div>
				<div className="flex items-center space-x-2">
					<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
						Previous
					</button>
					<span className="px-3 py-1 text-sm">Page 1 of 1</span>
					<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
						Next
					</button>
				</div>
			</div>
		</div>
	)
}