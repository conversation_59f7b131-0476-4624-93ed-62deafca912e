import type { ProcurementCase } from "../../types/procurement"
import { getCaseDetails } from "../../data/mockData"

interface DocumentAnalysisTabProps {
	caseItem: ProcurementCase
}

export default function DocumentAnalysisTab({ caseItem }: DocumentAnalysisTabProps) {
	// Get real case data from realistic database
	const detailedCase = getCaseDetails(caseItem.id)
	
	// Generate realistic document similarity data based on case details
	const competitors = detailedCase?.competitors || []
	const documentSimilarity = []
	
	if (detailedCase?.violations.includes("Document Similarity")) {
		// High-risk case with real document similarity issues
		competitors.slice(0, 2).forEach((competitor, index) => {
			const baseSimilarity = caseItem.riskScore >= 80 ? 0.85 + (Math.random() * 0.1) : 0.65 + (Math.random() * 0.15)
			documentSimilarity.push({
				docA: `${caseItem.winner} Proposal`,
				docB: `${competitor.name} Proposal`,
				similarity: Math.min(0.95, baseSimilarity),
				sections: index === 0 ? 
					["Technical Approach", "Implementation Timeline", "Resource Allocation"] :
					["Cost Structure", "Risk Management", "Quality Assurance"]
			})
		})
		
		// Add inter-competitor similarity if high risk
		if (competitors.length >= 2 && caseItem.riskScore >= 80) {
			documentSimilarity.push({
				docA: `${competitors[0].name} Proposal`,
				docB: `${competitors[1].name} Proposal`,
				similarity: 0.75 + (Math.random() * 0.1),
				sections: ["Technical Specifications", "Project Methodology"]
			})
		}
	} else {
		// Low/medium risk case with normal variation
		competitors.slice(0, 2).forEach((competitor, index) => {
			documentSimilarity.push({
				docA: `${caseItem.winner} Proposal`,
				docB: `${competitor.name} Proposal`,
				similarity: 0.25 + (Math.random() * 0.35), // Normal 25-60% similarity
				sections: index === 0 ? 
					["Standard Requirements"] :
					["Basic Structure"]
			})
		})
	}

	// Generate specification analysis based on real case data
	const specificationAnalysis = []
	const isSpecRigging = detailedCase?.violations.includes("Specification Rigging")
	const totalBidders = caseItem.bidders
	
	if (isSpecRigging) {
		// High-risk cases with specification rigging
		const suspiciousPatterns = detailedCase?.evidence?.bidManipulation?.suspiciousPatterns || []
		
		// Generate requirements based on actual suspicious patterns
		suspiciousPatterns.forEach((pattern) => {
			if (pattern.includes("specific") || pattern.includes("favoring")) {
				specificationAnalysis.push({
					requirement: `Highly specific requirement: ${pattern}`,
					specificity: "Very High",
					suspicion: "High",
					reason: "Overly specific requirement favoring particular vendor",
					matchedBy: [caseItem.winner]
				})
			}
		})
		
		// Add department-specific requirements
		const deptType = detailedCase?.department || ""
		if (deptType.includes("IT") || deptType.includes("Technology")) {
			specificationAnalysis.push({
				requirement: "Must have experience with legacy government systems",
				specificity: "High",
				suspicion: "High",
				reason: "Limits competition to established vendors",
				matchedBy: [caseItem.winner, competitors[0]?.name].filter(Boolean)
			})
		} else if (deptType.includes("Health")) {
			specificationAnalysis.push({
				requirement: "FDA approval for specific device model required",
				specificity: "Very High", 
				suspicion: "High",
				reason: "Requirement matches only one manufacturer",
				matchedBy: [caseItem.winner]
			})
		}
	} else {
		// Normal cases with standard requirements
		specificationAnalysis.push({
			requirement: "Industry certification required",
			specificity: "Medium",
			suspicion: "Low",
			reason: "Standard industry requirement",
			matchedBy: competitors.slice(0, Math.min(4, totalBidders)).map(c => c.name).concat([caseItem.winner])
		})
		
		specificationAnalysis.push({
			requirement: "Minimum experience threshold",
			specificity: "Medium",
			suspicion: "Low", 
			reason: "Reasonable experience requirement",
			matchedBy: competitors.slice(0, Math.min(3, totalBidders)).map(c => c.name).concat([caseItem.winner])
		})
	}

	// Use real timeline data from the case
	const timelineAnalysis: Array<{
		event: string;
		date: string;
		time: string;
		notes: string;
		suspicious?: boolean;
	}> = []
	if (detailedCase?.timeline) {
		detailedCase.timeline.forEach((event) => {
			timelineAnalysis.push({
				event: event.event,
				date: event.date,
				time: "09:00", // Default time since not in data
				notes: event.note || "Standard process",
				suspicious: event.suspicious || false
			})
		})
		
		// Add submission timing analysis for high-risk cases
		if (caseItem.riskScore >= 70) {
			const submissionEvent = timelineAnalysis.find(e => e.event.includes("Submit") || e.event.includes("Bid"))
			if (submissionEvent) {
				submissionEvent.notes = `${caseItem.bidders} proposals submitted - timing patterns under review`
				submissionEvent.suspicious = true
			}
		}
	} else {
		// Fallback timeline
		timelineAnalysis.push(
			{
				event: "RFP Published",
				date: caseItem.datePosted,
				time: "09:00",
				notes: "Standard publication time"
			},
			{
				event: "Proposals Due",
				date: caseItem.deadline,
				time: "15:00",
				notes: `${caseItem.bidders} proposals received`,
				suspicious: caseItem.riskScore >= 70
			}
		)
	}

	// Generate content overlap based on risk level and violations
	const contentOverlap = []
	const hasDocSimilarity = detailedCase?.violations.includes("Document Similarity")
	
	if (hasDocSimilarity && caseItem.riskScore >= 70) {
		// High-risk cases with significant overlap
		contentOverlap.push(
			{
				section: "Technical Specifications",
				overlap: 85 + Math.floor(Math.random() * 10),
				details: "Nearly identical technical requirements and specifications"
			},
			{
				section: "Implementation Approach", 
				overlap: 78 + Math.floor(Math.random() * 15),
				details: "Same project phases and milestone definitions"
			},
			{
				section: "Cost Breakdown",
				overlap: 82 + Math.floor(Math.random() * 10),
				details: "Suspiciously similar pricing structures"
			}
		)
	} else if (caseItem.riskScore >= 40) {
		// Medium-risk cases with moderate overlap
		contentOverlap.push(
			{
				section: "Project Methodology",
				overlap: 45 + Math.floor(Math.random() * 20),
				details: "Some similarities in approach but within normal range"
			},
			{
				section: "Quality Standards",
				overlap: 35 + Math.floor(Math.random() * 25), 
				details: "Standard industry practices referenced"
			}
		)
	} else {
		// Low-risk cases with normal variation
		contentOverlap.push(
			{
				section: "Standard Requirements",
				overlap: 25 + Math.floor(Math.random() * 20),
				details: "Normal overlap in standard industry practices"
			},
			{
				section: "Compliance Sections",
				overlap: 30 + Math.floor(Math.random() * 15),
				details: "Similar regulatory compliance language"
			}
		)
	}

	const getSimilarityColor = (similarity: number) => {
		if (similarity >= 0.8) return "text-red-600 bg-red-50"
		if (similarity >= 0.6) return "text-orange-600 bg-orange-50"
		return "text-yellow-600 bg-yellow-50"
	}

	const getSuspicionColor = (level: string) => {
		switch (level) {
			case "High": return "text-red-600 bg-red-50"
			case "Medium": return "text-orange-600 bg-orange-50"
			case "Low": return "text-green-600 bg-green-50"
			default: return "text-gray-600 bg-gray-50"
		}
	}

	return (
		<div className="p-6 space-y-8">
			{/* Document Similarity Heatmap */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Document Similarity Analysis</h3>
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Similarity Matrix */}
					<div>
						<h4 className="font-medium text-gray-700 mb-3">Similarity Heatmap</h4>
						<div className="bg-white border rounded-lg p-4">
							<div className="space-y-3">
								{documentSimilarity.map((sim, index) => (
									<div key={index} className={`border rounded-lg p-3 ${getSimilarityColor(sim.similarity)}`}>
										<div className="flex justify-between items-center mb-2">
											<div className="text-sm font-medium">
												{sim.docA} ↔ {sim.docB}
											</div>
											<div className="text-lg font-bold">
												{(sim.similarity * 100).toFixed(0)}%
											</div>
										</div>
										<div className="text-xs opacity-75">
											Similar sections: {sim.sections.join(", ")}
										</div>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Content Overlap Details */}
					<div>
						<h4 className="font-medium text-gray-700 mb-3">Section-by-Section Overlap</h4>
						<div className="space-y-3">
							{contentOverlap.map((section, index) => (
								<div key={index} className="bg-white border rounded-lg p-3">
									<div className="flex justify-between items-center mb-2">
										<span className="font-medium text-gray-900">{section.section}</span>
										<span className={`px-2 py-1 rounded text-xs font-medium ${
											section.overlap >= 80 ? "bg-red-100 text-red-800" :
											section.overlap >= 60 ? "bg-orange-100 text-orange-800" :
											"bg-yellow-100 text-yellow-800"
										}`}>
											{section.overlap}%
										</span>
									</div>
									<div className="text-sm text-gray-600">{section.details}</div>
									<div className="mt-2">
										<div className="w-full bg-gray-200 rounded-full h-2">
											<div
												className={`h-2 rounded-full ${
													section.overlap >= 80 ? "bg-red-500" :
													section.overlap >= 60 ? "bg-orange-500" :
													"bg-yellow-500"
												}`}
												style={{ width: `${section.overlap}%` }}
											/>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Specification Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Specification Rigging Analysis</h3>
				<div className="bg-white border rounded-lg overflow-hidden">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Requirement</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Specificity</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Suspicion Level</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Reason</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Qualified Bidders</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{specificationAnalysis.map((spec, index) => (
								<tr key={index}>
									<td className="px-4 py-3 text-sm text-gray-900">{spec.requirement}</td>
									<td className="px-4 py-3 text-center">
										<span className={`px-2 py-1 rounded text-xs font-medium ${
											spec.specificity === "Very High" ? "bg-red-100 text-red-800" :
											spec.specificity === "High" ? "bg-orange-100 text-orange-800" :
											spec.specificity === "Medium" ? "bg-yellow-100 text-yellow-800" :
											"bg-green-100 text-green-800"
										}`}>
											{spec.specificity}
										</span>
									</td>
									<td className="px-4 py-3 text-center">
										<span className={`px-2 py-1 rounded text-xs font-medium ${getSuspicionColor(spec.suspicion)}`}>
											{spec.suspicion}
										</span>
									</td>
									<td className="px-4 py-3 text-sm text-gray-600">{spec.reason}</td>
									<td className="px-4 py-3 text-center">
										<span className={`px-2 py-1 rounded text-xs font-medium ${
											spec.matchedBy.length === 1 ? "bg-red-100 text-red-800" :
											spec.matchedBy.length === 2 ? "bg-orange-100 text-orange-800" :
											"bg-green-100 text-green-800"
										}`}>
											{spec.matchedBy.length} of {caseItem.bidders}
										</span>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Timeline Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Document Submission Timeline</h3>
				<div className="bg-white border rounded-lg p-6">
					<div className="space-y-4">
						{timelineAnalysis.map((event, index) => (
							<div key={index} className={`flex items-center space-x-4 p-3 rounded-lg ${
								event.suspicious ? "bg-red-50 border border-red-200" : "bg-gray-50"
							}`}>
								<div className="flex-shrink-0">
									<div className={`w-3 h-3 rounded-full ${
										event.suspicious ? "bg-red-500" : "bg-blue-500"
									}`} />
								</div>
								<div className="flex-1">
									<div className="flex items-center space-x-4">
										<span className="font-medium text-gray-900">{event.event}</span>
										<span className="text-sm text-gray-600">{event.date} at {event.time}</span>
									</div>
									<div className={`text-sm mt-1 ${
										event.suspicious ? "text-red-700" : "text-gray-600"
									}`}>
										{event.notes}
									</div>
								</div>
								{event.suspicious && (
									<div className="flex-shrink-0">
										<span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">
											Suspicious
										</span>
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Dynamic Key Findings Summary */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Key Findings</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					{caseItem.riskScore >= 70 ? (
						<div className="bg-red-50 border border-red-200 rounded-lg p-4">
							<div className="flex items-center space-x-2 mb-2">
								<span className="text-red-600 text-lg">🚨</span>
								<span className="font-medium text-red-900">Critical Risk Indicators</span>
							</div>
							<ul className="space-y-1 text-sm text-red-700">
								{hasDocSimilarity && <li>• High document similarity detected</li>}
								{isSpecRigging && <li>• Specification rigging patterns identified</li>}
								{detailedCase?.evidence?.bidManipulation?.suspiciousPatterns && detailedCase.evidence.bidManipulation.suspiciousPatterns.length > 0 && 
									<li>• {detailedCase.evidence.bidManipulation.suspiciousPatterns.length} suspicious bid patterns</li>}
								<li>• Risk Score: {caseItem.riskScore}/100</li>
								{timelineAnalysis.some(t => t.suspicious) && <li>• Suspicious timeline events detected</li>}
							</ul>
						</div>
					) : caseItem.riskScore >= 40 ? (
						<div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
							<div className="flex items-center space-x-2 mb-2">
								<span className="text-orange-600 text-lg">⚠️</span>
								<span className="font-medium text-orange-900">Medium Risk Indicators</span>
							</div>
							<ul className="space-y-1 text-sm text-orange-700">
								<li>• Moderate document overlap detected</li>
								<li>• Standard competition patterns</li>
								<li>• {caseItem.bidders} bidders participated</li>
								<li>• Risk Score: {caseItem.riskScore}/100</li>
							</ul>
						</div>
					) : (
						<div className="bg-green-50 border border-green-200 rounded-lg p-4">
							<div className="flex items-center space-x-2 mb-2">
								<span className="text-green-600 text-lg">✅</span>
								<span className="font-medium text-green-900">Low Risk Assessment</span>
							</div>
							<ul className="space-y-1 text-sm text-green-700">
								<li>• Normal document variation patterns</li>
								<li>• Healthy competition with {caseItem.bidders} bidders</li>
								<li>• No significant red flags identified</li>
								<li>• Risk Score: {caseItem.riskScore}/100</li>
							</ul>
						</div>
					)}
					
					<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
						<div className="flex items-center space-x-2 mb-2">
							<span className="text-blue-600 text-lg">📊</span>
							<span className="font-medium text-blue-900">Case Summary</span>
						</div>
						<ul className="space-y-1 text-sm text-blue-700">
							<li>• Case ID: {caseItem.id}</li>
							<li>• Status: {caseItem.status}</li>
							<li>• Winner: {caseItem.winner}</li>
							<li>• Total Bidders: {caseItem.bidders}</li>
							<li>• Value: ${caseItem.amount.toLocaleString()}</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	)
}