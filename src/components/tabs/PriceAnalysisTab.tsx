import { getCaseDetails } from "../../data/mockData"
import type { ProcurementCase } from "../../types/procurement"
import { formatCurrency } from "../../utils/riskUtils"

interface PriceAnalysisTabProps {
	caseItem: ProcurementCase
}

export default function PriceAnalysisTab({ caseItem }: PriceAnalysisTabProps) {
	// Get detailed case data from realistic database
	const detailedCase = getCaseDetails(caseItem.id)
	
	// Use realistic data or fallback to calculated values
	const marketRate = detailedCase?.evidence?.bidManipulation?.priceAnalysis?.marketAverage || caseItem.amount * 1.15
	const deviation = ((caseItem.amount - marketRate) / marketRate) * 100
	
	// Build bid data from realistic database
	const bidData = []
	if (detailedCase) {
		// Add winner
		bidData.push({
			company: caseItem.winner,
			amount: detailedCase.evidence?.bidManipulation?.priceAnalysis?.winnerBid || caseItem.amount,
			rank: 1,
			status: "winner"
		})
		
		// Add competitors with their bids
		detailedCase.competitors?.forEach((competitor, index) => {
			bidData.push({
				company: competitor.name,
				amount: competitor.bid,
				rank: index + 2,
				status: "lost"
			})
		})
	} else {
		// Fallback to generic data
		bidData.push(
			{ company: caseItem.winner, amount: caseItem.amount, rank: 1, status: "winner" },
			{ company: "CompetitorA", amount: caseItem.amount * 1.02, rank: 2, status: "lost" },
			{ company: "CompetitorB", amount: caseItem.amount * 1.03, rank: 3, status: "lost" },
			{ company: "CompetitorC", amount: caseItem.amount * 1.25, rank: 4, status: "lost" },
			{ company: "CompetitorD", amount: caseItem.amount * 1.45, rank: 5, status: "lost" },
		)
	}

	const historicalPrices = [
		{ date: "2024-Q1", price: caseItem.amount * 0.9, project: "Similar IT Project" },
		{ date: "2024-Q2", price: caseItem.amount * 0.95, project: "Infrastructure Upgrade" },
		{ date: "2024-Q3", price: caseItem.amount * 1.1, project: "System Modernization" },
		{ date: "2024-Q4", price: caseItem.amount * 1.05, project: "Digital Transformation" },
		{ date: "2025-Q1", price: caseItem.amount, project: "Current Project" },
	]

	const costBreakdown = [
		{ category: "Hardware", budgeted: caseItem.amount * 0.4, proposed: caseItem.amount * 0.35, variance: -12.5 },
		{ category: "Software Licenses", budgeted: caseItem.amount * 0.3, proposed: caseItem.amount * 0.25, variance: -16.7 },
		{ category: "Implementation", budgeted: caseItem.amount * 0.2, proposed: caseItem.amount * 0.25, variance: 25.0 },
		{ category: "Training", budgeted: caseItem.amount * 0.1, proposed: caseItem.amount * 0.15, variance: 50.0 },
	]

	const maxHistoricalPrice = Math.max(...historicalPrices.map(h => h.price))

	return (
		<div className="p-6 space-y-8">
			{/* Price Overview */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
					<h4 className="font-medium text-blue-900 mb-2">Winning Bid</h4>
					<div className="text-2xl font-bold text-blue-900">{formatCurrency(caseItem.amount)}</div>
					<div className="text-sm text-blue-700">by {caseItem.winner}</div>
				</div>
				
				<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
					<h4 className="font-medium text-gray-900 mb-2">Market Rate Estimate</h4>
					<div className="text-2xl font-bold text-gray-900">{formatCurrency(marketRate)}</div>
					<div className="text-sm text-gray-600">Based on similar projects</div>
				</div>
				
				<div className={`border rounded-lg p-4 ${
					deviation < -10 ? "bg-red-50 border-red-200" : 
					deviation < -5 ? "bg-yellow-50 border-yellow-200" : 
					"bg-green-50 border-green-200"
				}`}>
					<h4 className={`font-medium mb-2 ${
						deviation < -10 ? "text-red-900" : 
						deviation < -5 ? "text-yellow-900" : 
						"text-green-900"
					}`}>Price Deviation</h4>
					<div className={`text-2xl font-bold ${
						deviation < -10 ? "text-red-900" : 
						deviation < -5 ? "text-yellow-900" : 
						"text-green-900"
					}`}>
						{deviation.toFixed(1)}%
					</div>
					<div className={`text-sm ${
						deviation < -10 ? "text-red-700" : 
						deviation < -5 ? "text-yellow-700" : 
						"text-green-600"
					}`}>
						{deviation < -10 ? "Suspiciously low" : deviation < -5 ? "Below market" : "Within range"}
					</div>
				</div>
			</div>

			{/* Bid Clustering Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Bid Clustering Analysis</h3>
				<div className="bg-white border rounded-lg overflow-hidden">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Rank</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Company</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Bid Amount</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Difference</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Clustering Risk</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{bidData.map((bid, index) => {
								const diffFromWinner = bid.amount - caseItem.amount
								const diffPercentage = (diffFromWinner / caseItem.amount) * 100
								const isCluster = diffPercentage < 5 && diffPercentage > 0
								
								return (
									<tr key={index} className={bid.status === "winner" ? "bg-blue-50" : ""}>
										<td className="px-4 py-3 text-sm text-gray-900">#{bid.rank}</td>
										<td className="px-4 py-3 text-sm font-medium text-gray-900">
											{bid.company}
											{bid.status === "winner" && (
												<span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
													Winner
												</span>
											)}
										</td>
										<td className="px-4 py-3 text-sm text-gray-900 text-right">
											{formatCurrency(bid.amount)}
										</td>
										<td className="px-4 py-3 text-sm text-gray-900 text-right">
											{diffFromWinner === 0 ? "-" : `+${formatCurrency(diffFromWinner)} (+${diffPercentage.toFixed(1)}%)`}
										</td>
										<td className="px-4 py-3 text-center">
											{isCluster ? (
												<span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
													Suspicious
												</span>
											) : diffPercentage > 20 ? (
												<span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
													Normal
												</span>
											) : (
												<span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
													Close
												</span>
											)}
										</td>
									</tr>
								)
							})}
						</tbody>
					</table>
				</div>
				
				{/* Clustering Alert - Dynamic based on actual data */}
				{(() => {
					const sortedBids = [...bidData].sort((a, b) => a.amount - b.amount)
					const topThree = sortedBids.slice(0, 3)
					const maxDiff = topThree.length > 1 ? 
						((topThree[topThree.length - 1].amount - topThree[0].amount) / topThree[0].amount) * 100 : 0
					
					const isHighRisk = caseItem.riskScore >= 70
					const isSuspiciousClustering = maxDiff < 5 && topThree.length >= 3
					
					if (isHighRisk && isSuspiciousClustering) {
						return (
							<div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
								<div className="flex items-center space-x-2">
									<span className="text-red-600 text-lg">🚨</span>
									<div>
										<div className="font-medium text-red-900">Critical: Bid Clustering Detected</div>
										<div className="text-sm text-red-700">
											Top {topThree.length} bids are within {maxDiff.toFixed(1)}% of each other, indicating likely price coordination
										</div>
									</div>
								</div>
							</div>
						)
					} else if (isSuspiciousClustering) {
						return (
							<div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
								<div className="flex items-center space-x-2">
									<span className="text-yellow-600 text-lg">⚠️</span>
									<div>
										<div className="font-medium text-yellow-900">Potential Bid Clustering</div>
										<div className="text-sm text-yellow-700">
											Top {topThree.length} bids are within {maxDiff.toFixed(1)}% of each other, requires review
										</div>
									</div>
								</div>
							</div>
						)
					} else if (isHighRisk) {
						return (
							<div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-4">
								<div className="flex items-center space-x-2">
									<span className="text-orange-600 text-lg">⚠️</span>
									<div>
										<div className="font-medium text-orange-900">High Risk Case</div>
										<div className="text-sm text-orange-700">
											Price analysis shows suspicious patterns - requires investigation
										</div>
									</div>
								</div>
							</div>
						)
					} else {
						return (
							<div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
								<div className="flex items-center space-x-2">
									<span className="text-green-600 text-lg">✅</span>
									<div>
										<div className="font-medium text-green-900">Normal Bid Distribution</div>
										<div className="text-sm text-green-700">
											Bid spacing appears normal with healthy competition
										</div>
									</div>
								</div>
							</div>
						)
					}
				})()}
			</div>

			{/* Historical Price Trends */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Historical Price Trends</h3>
				<div className="bg-white border rounded-lg p-6">
					<div className="relative h-64">
						<svg width="100%" height="100%" viewBox="0 0 100 80" className="absolute inset-0">
							{/* Grid lines */}
							{[0, 20, 40, 60, 80].map(y => (
								<line key={y} x1="10" y1={y} x2="95" y2={y} stroke="#e5e7eb" strokeWidth="0.2" />
							))}
							{historicalPrices.map((_, index) => {
								const x = 10 + (index * 85 / (historicalPrices.length - 1))
								return (
									<line key={index} x1={x} y1="10" x2={x} y2="70" stroke="#e5e7eb" strokeWidth="0.2" />
								)
							})}
							
							{/* Price line */}
							<polyline
								points={historicalPrices.map((price, index) => {
									const x = 10 + (index * 85 / (historicalPrices.length - 1))
									const y = 70 - ((price.price / maxHistoricalPrice) * 50)
									return `${x},${y}`
								}).join(" ")}
								fill="none"
								stroke="#3b82f6"
								strokeWidth="0.8"
							/>
							
							{/* Data points */}
							{historicalPrices.map((price, index) => {
								const x = 10 + (index * 85 / (historicalPrices.length - 1))
								const y = 70 - ((price.price / maxHistoricalPrice) * 50)
								const isCurrent = index === historicalPrices.length - 1
								
								return (
									<g key={index}>
										<circle
											cx={x}
											cy={y}
											r={isCurrent ? "1.5" : "1"}
											fill={isCurrent ? "#dc2626" : "#3b82f6"}
										/>
										<text
											x={x}
											y="75"
											textAnchor="middle"
											fontSize="2"
											fill="#6b7280"
										>
											{price.date}
										</text>
									</g>
								)
							})}
						</svg>
						
						{/* Y-axis labels */}
						<div className="absolute left-0 top-2 text-xs text-gray-600">
							{formatCurrency(maxHistoricalPrice)}
						</div>
						<div className="absolute left-0 bottom-8 text-xs text-gray-600">
							{formatCurrency(maxHistoricalPrice * 0.5)}
						</div>
					</div>
					
					<div className="mt-4 text-sm text-gray-600">
						<div className="flex items-center space-x-4">
							<div className="flex items-center space-x-2">
								<div className="w-3 h-3 bg-blue-600 rounded-full"></div>
								<span>Historical Prices</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-3 h-3 bg-red-600 rounded-full"></div>
								<span>Current Project</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Cost Breakdown Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Cost Breakdown Analysis</h3>
				<div className="bg-white border rounded-lg overflow-hidden">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Budgeted</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Proposed</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Variance</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Risk</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{costBreakdown.map((item, index) => (
								<tr key={index}>
									<td className="px-4 py-3 text-sm font-medium text-gray-900">{item.category}</td>
									<td className="px-4 py-3 text-sm text-gray-900 text-right">
										{formatCurrency(item.budgeted)}
									</td>
									<td className="px-4 py-3 text-sm text-gray-900 text-right">
										{formatCurrency(item.proposed)}
									</td>
									<td className={`px-4 py-3 text-sm text-right font-medium ${
										item.variance > 15 ? "text-red-600" : 
										item.variance < -10 ? "text-red-600" : 
										"text-gray-900"
									}`}>
										{item.variance > 0 ? "+" : ""}{item.variance.toFixed(1)}%
									</td>
									<td className="px-4 py-3 text-center">
										{Math.abs(item.variance) > 15 ? (
											<span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
												High
											</span>
										) : Math.abs(item.variance) > 10 ? (
											<span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
												Medium
											</span>
										) : (
											<span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
												Low
											</span>
										)}
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	)
}