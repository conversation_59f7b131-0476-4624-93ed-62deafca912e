import { useCallback, useMemo, useState } from "react"
import { motion } from "framer-motion"
import { 
	ReactFlow, 
	addEdge, 
	useNodesState, 
	useEdgesState,
	Controls,
	MiniMap,
	Background,
	BackgroundVariant,
	type Node,
	type Edge,
	type Connection,
	MarkerType
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { Network, AlertCircle, Users, MapPin, Phone, Mail, Maximize2 } from "lucide-react"
import type { ProcurementCase } from "../../types/procurement"
import CustomCompanyNode from "../CustomCompanyNode"
import { getCaseDetails, getCompanyDetails } from "../../data/mockData"

interface CompanyRelationshipsTabProps {
	caseItem: ProcurementCase
}

export default function CompanyRelationshipsTab({ caseItem }: CompanyRelationshipsTabProps) {
	const [selectedNode, setSelectedNode] = useState<string | null>(null)
	const [isFullscreen, setIsFullscreen] = useState(false)

	// Get detailed case information
	const caseDetails = getCaseDetails(caseItem.id)
	
	// Create network data based on real case data
	const initialNodes: Node[] = useMemo(() => {
		if (!caseDetails) {
			// Fallback to basic data if case details not found
			return [
				{
					id: 'winner',
					type: 'custom',
					position: { x: 250, y: 100 },
					data: { 
						label: caseItem.winner,
						type: 'winner',
						riskLevel: caseItem.riskScore >= 80 ? 'critical' : caseItem.riskScore >= 60 ? 'high' : 'medium'
					},
				}
			]
		}

		const nodes: Node[] = []
		
		// Add winner node in better center position
		nodes.push({
			id: 'winner',
			type: 'custom',
			position: { x: 500, y: 300 }, // Much more centered for better spacing
			data: { 
				label: caseDetails.winner,
				type: 'winner',
				riskLevel: caseDetails.riskScore >= 80 ? 'critical' : caseDetails.riskScore >= 60 ? 'high' : 'medium'
			},
		})

		// Add competitor nodes with better spacing
		caseDetails.competitors.forEach((competitor, index) => {
			let x, y
			const competitorCount = caseDetails.competitors.length
			
			if (competitorCount === 1) {
				// Single competitor - place to the right with much more space
				x = 800
				y = 300
			} else if (competitorCount === 2) {
				// Two competitors - place with much more spacing
				x = index === 0 ? 200 : 800
				y = 300
			} else if (competitorCount === 3) {
				// Three competitors - triangle formation with much more spacing
				const positions = [
					{ x: 200, y: 100 },   // Top left
					{ x: 800, y: 100 },   // Top right
					{ x: 500, y: 550 }    // Bottom center
				]
				x = positions[index].x
				y = positions[index].y
			} else {
				// More than 3 - use circular arrangement with much larger radius
				const angle = (index * 2 * Math.PI) / competitorCount
				const radius = Math.max(400, competitorCount * 80) // Much larger radius and spacing
				x = 500 + radius * Math.cos(angle)
				y = 300 + radius * Math.sin(angle)
			}
			
			nodes.push({
				id: `competitor-${index}`,
				type: 'custom',
				position: { x, y },
				data: { 
					label: competitor.name,
					type: 'competitor',
					riskLevel: competitor.riskLevel as any
				},
			})
		})

		// Add shared directors as person nodes with much better spacing
		if (caseDetails.evidence.networkConnections.sharedDirectors.length > 0) {
			caseDetails.evidence.networkConnections.sharedDirectors.forEach((director, index) => {
				const directorCount = caseDetails.evidence.networkConnections.sharedDirectors.length
				let x, y
				
				if (directorCount === 1) {
					x = 500
					y = 600
				} else {
					// Spread directors horizontally below the network with much more space
					const spacing = Math.min(200, 600 / directorCount) // Much more adaptive spacing
					const startX = 500 - ((directorCount - 1) * spacing) / 2
					x = startX + index * spacing
					y = 600
				}
				
				nodes.push({
					id: `person-${index}`,
					type: 'custom',
					position: { x, y },
					data: { 
						label: `${director.name}\nDirector`,
						type: 'person',
						riskLevel: 'critical'
					},
				})
			})
		}

		// Add holding companies if they exist
		const holdingCompanies = caseDetails.evidence.networkConnections.sharedDirectors
			.flatMap(director => director.companies)
			.filter(company => company !== caseDetails.winner && !caseDetails.competitors.some(c => c.name === company))
			.filter((company, index, arr) => arr.indexOf(company) === index) // Remove duplicates

		holdingCompanies.forEach((holding, index) => {
			const holdingCount = holdingCompanies.length
			let x, y
			
			if (holdingCount === 1) {
				x = 500
				y = 350
			} else {
				// Position holdings to the right side, vertically stacked
				x = 550 + (index % 2) * 100  // Two columns if many holdings
				y = 300 + Math.floor(index / 2) * 100
			}
			
			nodes.push({
				id: `holding-${index}`,
				type: 'custom',
				position: { x, y },
				data: { 
					label: `${holding}\nHolding Co.`,
					type: 'holding',
					riskLevel: 'medium'
				},
			})
		})

		return nodes
	}, [caseItem.id, caseItem.winner, caseItem.riskScore, caseDetails])

	const initialEdges: Edge[] = useMemo(() => {
		if (!caseDetails) return []

		const edges: Edge[] = []
		let edgeId = 0

		// Create edges based on shared directors
		caseDetails.evidence.networkConnections.sharedDirectors.forEach((director, directorIndex) => {
			director.companies.forEach(company => {
				if (company === caseDetails.winner) {
					// Connect winner to director
					edges.push({
						id: `e-winner-person-${directorIndex}`,
						source: 'winner',
						target: `person-${directorIndex}`,
						type: 'smoothstep',
						label: '🔗 Shared Director',
						style: { 
							stroke: '#dc2626', 
							strokeWidth: 3,
							filter: 'drop-shadow(0 2px 4px rgba(220, 38, 38, 0.3))'
						},
						labelStyle: { 
							fontSize: '12px', 
							fontWeight: 'bold', 
							fill: '#dc2626',
							background: '#fee2e2',
							padding: '4px 8px',
							borderRadius: '6px',
							border: '1px solid #dc2626'
						},
						animated: true,
						markerEnd: {
							type: MarkerType.ArrowClosed,
							color: '#dc2626',
						}
					})
				} else {
					// Find competitor and connect to director
					const competitorIndex = caseDetails.competitors.findIndex(c => c.name === company)
					if (competitorIndex !== -1) {
						edges.push({
							id: `e-comp-${competitorIndex}-person-${directorIndex}`,
							source: `competitor-${competitorIndex}`,
							target: `person-${directorIndex}`,
							type: 'smoothstep',
							label: '🔗 Same Director',
							style: { 
								stroke: '#ea580c', 
								strokeWidth: 3,
								filter: 'drop-shadow(0 2px 4px rgba(234, 88, 12, 0.3))'
							},
							labelStyle: { 
								fontSize: '12px', 
								fontWeight: 'bold', 
								fill: '#ea580c',
								background: '#fed7aa',
								padding: '4px 8px',
								borderRadius: '6px',
								border: '1px solid #ea580c'
							},
							animated: true,
							markerEnd: {
								type: MarkerType.ArrowClosed,
								color: '#ea580c',
							}
						})
					}
				}
			})
		})

		// Create edges based on address clusters
		caseDetails.evidence.networkConnections.addressClusters.forEach(cluster => {
			cluster.companies.forEach(company => {
				if (company === caseDetails.winner) {
					// Connect winner to competitors in same cluster
					cluster.companies.forEach(otherCompany => {
						if (otherCompany !== company) {
							const competitorIndex = caseDetails.competitors.findIndex(c => c.name === otherCompany)
							if (competitorIndex !== -1) {
								edges.push({
									id: `e-addr-winner-${competitorIndex}`,
									source: 'winner',
									target: `competitor-${competitorIndex}`,
									type: 'smoothstep',
									label: '📍 Same Address',
									style: { 
										stroke: '#d97706', 
										strokeWidth: 2, 
										strokeDasharray: '8,4',
										filter: 'drop-shadow(0 2px 4px rgba(217, 119, 6, 0.2))'
									},
									labelStyle: { 
										fontSize: '11px', 
										fill: '#d97706',
										background: '#fef3c7',
										padding: '3px 6px',
										borderRadius: '4px',
										border: '1px solid #d97706'
									}
								})
							}
						}
					})
				}
			})
		})

		// Create edges based on contact overlaps
		caseDetails.evidence.networkConnections.contactOverlaps.forEach(contact => {
			contact.companies.forEach((company1, index1) => {
				contact.companies.forEach((company2, index2) => {
					if (index1 < index2) { // Avoid duplicate edges
						let source = '', target = '', label = ''
						
						// Determine source and target
						if (company1 === caseDetails.winner) {
							source = 'winner'
						} else {
							const compIndex = caseDetails.competitors.findIndex(c => c.name === company1)
							if (compIndex !== -1) source = `competitor-${compIndex}`
						}
						
						if (company2 === caseDetails.winner) {
							target = 'winner'
						} else {
							const compIndex = caseDetails.competitors.findIndex(c => c.name === company2)
							if (compIndex !== -1) target = `competitor-${compIndex}`
						}

						// Determine label based on contact type
						if (contact.type === 'phone') {
							label = '📞 Same Phone'
						} else if (contact.type === 'email_domain') {
							label = '📧 Same Email Domain'
						} else {
							label = '📞 Same Contact'
						}

						if (source && target && source !== target) {
							edges.push({
								id: `e-contact-${edgeId++}`,
								source,
								target,
								type: 'smoothstep',
								label,
								style: { 
									stroke: contact.riskLevel === 'Critical' ? '#dc2626' : '#ea580c', 
									strokeWidth: contact.riskLevel === 'Critical' ? 3 : 2,
									filter: `drop-shadow(0 2px 4px rgba(${contact.riskLevel === 'Critical' ? '220, 38, 38' : '234, 88, 12'}, 0.3))`
								},
								labelStyle: { 
									fontSize: '11px', 
									fontWeight: 'bold', 
									fill: contact.riskLevel === 'Critical' ? '#dc2626' : '#ea580c',
									background: contact.riskLevel === 'Critical' ? '#fee2e2' : '#fed7aa',
									padding: '3px 6px',
									borderRadius: '4px',
									border: `1px solid ${contact.riskLevel === 'Critical' ? '#dc2626' : '#ea580c'}`
								},
								animated: contact.riskLevel === 'Critical',
								markerEnd: {
									type: MarkerType.ArrowClosed,
									color: contact.riskLevel === 'Critical' ? '#dc2626' : '#ea580c',
								}
							})
						}
					}
				})
			})
		})

		return edges
	}, [caseDetails])

	const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
	const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

	const onConnect = useCallback((params: Connection) => setEdges((eds) => addEdge(params, eds)), [setEdges])

	// Node types
	const nodeTypes = useMemo(() => ({
		custom: CustomCompanyNode,
	}), [])

	// Handle node selection
	const onNodeClick = useCallback((_event: any, node: Node) => {
		setSelectedNode(selectedNode === node.id ? null : node.id)
		
		// Highlight connected edges
		setEdges((eds) => eds.map((edge) => ({
			...edge,
			style: {
				...edge.style,
				opacity: edge.source === node.id || edge.target === node.id ? 1 : 0.3,
			}
		})))
		
		// Highlight connected nodes
		setNodes((nds) => nds.map((n) => ({
			...n,
			style: {
				...n.style,
				opacity: n.id === node.id || 
					edges.some(e => (e.source === node.id && e.target === n.id) || (e.target === node.id && e.source === n.id)) 
					? 1 : 0.5,
			}
		})))
	}, [selectedNode, edges, setEdges, setNodes])

	// Reset highlighting
	const resetHighlight = useCallback(() => {
		setSelectedNode(null)
		setEdges((eds) => eds.map((edge) => ({ ...edge, style: { ...edge.style, opacity: 1 } })))
		setNodes((nds) => nds.map((node) => ({ ...node, style: { ...node.style, opacity: 1 } })))
	}, [setEdges, setNodes])

	const nodeColors = {
		winner: '#dc2626',
		competitor: '#ea580c',
		person: '#3b82f6',
		holding: '#d97706'
	}

	// Get realistic address clusters from case data
	const addressClusters = useMemo(() => {
		if (!caseDetails) return []
		
		return caseDetails.evidence.networkConnections.addressClusters.map(cluster => ({
			address: cluster.address,
			companies: cluster.companies,
			suspiciousLevel: cluster.riskLevel === "Critical" ? "High" : "Medium",
		}))
	}, [caseDetails])

	return (
		<div className="p-6 space-y-8">
			{/* Interactive Network Visualization */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
			>
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center space-x-3">
						<Network className="w-6 h-6 text-blue-600" />
						<h3 className="text-lg font-semibold text-gray-900">🕸️ Company Network Visualization</h3>
					</div>
					<div className="flex items-center space-x-4">
						{/* Dynamic risk alert based on actual case data */}
						{caseItem.riskScore >= 70 && caseDetails?.evidence?.networkConnections ? (
							<motion.div 
								className="flex items-center space-x-2 text-sm text-red-600"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.5 }}
							>
								<AlertCircle className="w-4 h-4" />
								<span className="font-medium">High Risk Network Detected</span>
							</motion.div>
						) : caseItem.riskScore >= 40 ? (
							<motion.div 
								className="flex items-center space-x-2 text-sm text-orange-600"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.5 }}
							>
								<AlertCircle className="w-4 h-4" />
								<span className="font-medium">Medium Risk Network</span>
							</motion.div>
						) : (
							<motion.div 
								className="flex items-center space-x-2 text-sm text-green-600"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.5 }}
							>
								<Network className="w-4 h-4" />
								<span className="font-medium">Low Risk Network</span>
							</motion.div>
						)}
						<div className="flex items-center space-x-2">
							<button 
								type="button"
								onClick={() => setIsFullscreen(!isFullscreen)}
								className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
							>
								<Maximize2 className="w-4 h-4" />
							</button>
							<button 
								type="button"
								onClick={resetHighlight}
								className="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors text-sm"
							>
								Reset View
							</button>
						</div>
					</div>
				</div>
				
				<div 
					className={`bg-white rounded-lg border shadow-lg overflow-hidden transition-all duration-300 ${
						isFullscreen ? 'fixed inset-4 z-50' : ''
					}`} 
					style={{ height: isFullscreen ? 'calc(100vh - 2rem)' : '600px' }}
				>
					<ReactFlow
						nodes={nodes}
						edges={edges}
						onNodesChange={onNodesChange}
						onEdgesChange={onEdgesChange}
						onConnect={onConnect}
						onNodeClick={onNodeClick}
						onPaneClick={resetHighlight}
						nodeTypes={nodeTypes}
						fitView
						attributionPosition="top-right"
						proOptions={{ hideAttribution: true }}
						defaultViewport={{ x: 0, y: 0, zoom: 1 }}
						minZoom={0.3}
						maxZoom={2}
						nodesDraggable={true}
						nodesConnectable={false}
						elementsSelectable={true}
						className="bg-gray-50"
					>
						<Controls 
							position="top-left"
						/>
						<MiniMap 
							nodeColor={(node) => nodeColors[node.data.type as keyof typeof nodeColors] || '#6b7280'}
							style={{
								height: 120,
								width: 200,
								backgroundColor: '#f8fafc',
								border: '2px solid #e2e8f0',
								borderRadius: '8px',
							}}
							maskColor="rgba(0, 0, 0, 0.1)"
						/>
						<Background 
							variant={BackgroundVariant.Dots} 
							gap={20} 
							size={1.5} 
							color="#e2e8f0"
						/>
					</ReactFlow>
					
					{isFullscreen && (
						<button
							type="button"
							onClick={() => setIsFullscreen(false)}
							className="absolute top-4 right-4 p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors z-10"
						>
							✕ Close
						</button>
					)}
				</div>
				
				{/* Network Legend & Stats */}
				<motion.div 
					className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 1 }}
				>
					{/* Legend */}
					<div className="bg-gray-50 rounded-lg p-4">
						<h4 className="font-semibold text-gray-800 mb-3">🎯 Network Legend</h4>
						<div className="grid grid-cols-2 gap-3 text-sm">
							<div className="flex items-center space-x-2">
								<div className="w-4 h-4 bg-red-600 rounded border-2 border-red-700"></div>
								<span>🏆 Winner (Critical Risk)</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-4 h-4 bg-orange-600 rounded border-2 border-orange-700"></div>
								<span>🏢 Competitors</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-4 h-4 bg-blue-600 rounded-full border-2 border-blue-700"></div>
								<span>👤 Shared Personnel</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-4 h-4 bg-purple-600 rounded border-2 border-purple-700"></div>
								<span>🏛️ Financial Entities</span>
							</div>
							<div className="flex items-center space-x-2 col-span-2">
								<div className="w-6 h-0.5 bg-red-600"></div>
								<span>🔗 Direct Connections (Animated = High Risk)</span>
							</div>
						</div>
					</div>

					{/* Dynamic Network Statistics based on real case data */}
					<div className={`rounded-lg p-4 ${
						caseItem.riskScore >= 70 ? 'bg-red-50 border border-red-200' :
						caseItem.riskScore >= 40 ? 'bg-orange-50 border border-orange-200' :
						'bg-green-50 border border-green-200'
					}`}>
						<h4 className={`font-semibold mb-3 ${
							caseItem.riskScore >= 70 ? 'text-red-800' :
							caseItem.riskScore >= 40 ? 'text-orange-800' :
							'text-green-800'
						}`}>⚠️ Risk Analysis</h4>
						<div className="space-y-2 text-sm">
							<div className="flex justify-between">
								<span className={caseItem.riskScore >= 70 ? 'text-red-700' : caseItem.riskScore >= 40 ? 'text-orange-700' : 'text-green-700'}>Connected Companies:</span>
								<span className={`font-semibold ${caseItem.riskScore >= 70 ? 'text-red-800' : caseItem.riskScore >= 40 ? 'text-orange-800' : 'text-green-800'}`}>
									{caseDetails ? (caseDetails.competitors?.length || 0) + 1 : 1} entities
								</span>
							</div>
							<div className="flex justify-between">
								<span className={caseItem.riskScore >= 70 ? 'text-red-700' : caseItem.riskScore >= 40 ? 'text-orange-700' : 'text-green-700'}>Shared Directors:</span>
								<span className={`font-semibold ${caseItem.riskScore >= 70 ? 'text-red-800' : caseItem.riskScore >= 40 ? 'text-orange-800' : 'text-green-800'}`}>
									{caseDetails?.evidence?.networkConnections?.sharedDirectors?.length || 0} people
								</span>
							</div>
							<div className="flex justify-between">
								<span className={caseItem.riskScore >= 70 ? 'text-red-700' : caseItem.riskScore >= 40 ? 'text-orange-700' : 'text-green-700'}>Network Links:</span>
								<span className={`font-semibold ${caseItem.riskScore >= 70 ? 'text-red-800' : caseItem.riskScore >= 40 ? 'text-orange-800' : 'text-green-800'}`}>
									{(caseDetails?.evidence?.networkConnections?.addressClusters?.length || 0) + 
									 (caseDetails?.evidence?.networkConnections?.contactOverlaps?.length || 0)} connections
								</span>
							</div>
							<div className="flex justify-between">
								<span className={caseItem.riskScore >= 70 ? 'text-red-700' : caseItem.riskScore >= 40 ? 'text-orange-700' : 'text-green-700'}>Risk Score:</span>
								<span className={`font-bold text-lg ${
									caseItem.riskScore >= 80 ? 'text-red-900' :
									caseItem.riskScore >= 60 ? 'text-orange-900' :
									caseItem.riskScore >= 30 ? 'text-yellow-800' :
									'text-green-800'
								}`}>
									{caseItem.riskScore >= 80 ? '🚨 CRITICAL' :
									 caseItem.riskScore >= 60 ? '⚠️ HIGH' :
									 caseItem.riskScore >= 30 ? '🟡 MEDIUM' :
									 '✅ LOW'} ({caseItem.riskScore}/100)
								</span>
							</div>
						</div>
					</div>
				</motion.div>

				{/* Selected Node Details */}
				{selectedNode && (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4"
					>
						<h4 className="font-semibold text-blue-800 mb-2">
							🔍 Selected: {String(nodes.find(n => n.id === selectedNode)?.data.label || 'Unknown')}
						</h4>
						<p className="text-blue-700 text-sm">
							Click on connected nodes and edges to explore relationships. 
							This entity has {edges.filter(e => e.source === selectedNode || e.target === selectedNode).length} direct connections.
						</p>
					</motion.div>
				)}
			</motion.div>

			{/* Analysis Panels */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
				{/* Shared Directors/Shareholders */}
				<motion.div
					initial={{ opacity: 0, x: -20 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ delay: 0.2 }}
				>
					<div className="flex items-center space-x-2 mb-4">
						<Users className="w-5 h-5 text-blue-600" />
						<h3 className="text-lg font-semibold text-gray-900">👥 Shared Directors & Shareholders</h3>
					</div>
					<div className="space-y-4">
						{/* Real shared directors from case data */}
						{caseDetails?.evidence?.networkConnections?.sharedDirectors && caseDetails.evidence.networkConnections.sharedDirectors.length > 0 ? (
							caseDetails.evidence.networkConnections.sharedDirectors.map((director, index) => (
								<motion.div 
									key={index}
									className={`rounded-lg p-4 ${
										caseItem.riskScore >= 70 ? 'bg-red-50 border border-red-200' :
										caseItem.riskScore >= 40 ? 'bg-orange-50 border border-orange-200' :
										'bg-green-50 border border-green-200'
									}`}
									whileHover={{ scale: 1.02 }}
									transition={{ type: "spring", stiffness: 300 }}
								>
									<div className="flex justify-between items-center mb-3">
										<div className="flex items-center space-x-2">
											<div className={`w-10 h-10 rounded-full flex items-center justify-center ${
												caseItem.riskScore >= 70 ? 'bg-red-100' :
												caseItem.riskScore >= 40 ? 'bg-orange-100' :
												'bg-green-100'
											}`}>
												👤
											</div>
											<div>
												<span className={`font-medium ${
													caseItem.riskScore >= 70 ? 'text-red-900' :
													caseItem.riskScore >= 40 ? 'text-orange-900' :
													'text-green-900'
												}`}>{director.name}</span>
												<div className={`text-xs ${
													caseItem.riskScore >= 70 ? 'text-red-700' :
													caseItem.riskScore >= 40 ? 'text-orange-700' :
													'text-green-700'
												}`}>Shared Director</div>
											</div>
										</div>
										<span className={`text-sm px-3 py-1 rounded-full font-medium ${
											caseItem.riskScore >= 70 ? 'bg-red-100 text-red-800' :
											caseItem.riskScore >= 40 ? 'bg-orange-100 text-orange-800' :
											'bg-green-100 text-green-800'
										}`}>
											{caseItem.riskScore >= 70 ? '🚨 High Risk' :
											 caseItem.riskScore >= 40 ? '⚠️ Medium Risk' :
											 '✅ Low Risk'}
										</span>
									</div>
									<div className={`text-sm mb-2 ${
										caseItem.riskScore >= 70 ? 'text-red-700' :
										caseItem.riskScore >= 40 ? 'text-orange-700' :
										'text-green-700'
									}`}>
										<strong>Director positions in:</strong>
										{director.companies.map((company, idx) => (
											<div key={idx}>• {company}</div>
										))}
									</div>
									{director.suspiciousIndicators && (
										<div className={`text-xs p-2 rounded ${
											caseItem.riskScore >= 70 ? 'text-red-600 bg-red-100' :
											caseItem.riskScore >= 40 ? 'text-orange-600 bg-orange-100' :
											'text-green-600 bg-green-100'
										}`}>
											⚠️ {director.suspiciousIndicators}
										</div>
									)}
								</motion.div>
							))
						) : (
							<motion.div 
								className="bg-green-50 border border-green-200 rounded-lg p-4"
								whileHover={{ scale: 1.02 }}
								transition={{ type: "spring", stiffness: 300 }}
							>
								<div className="flex items-center space-x-2">
									<div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
										✅
									</div>
									<div>
										<span className="font-medium text-green-900">No Shared Directors Detected</span>
										<div className="text-xs text-green-700">No overlapping personnel found between companies</div>
									</div>
								</div>
							</motion.div>
						)}
						
						{/* Contact overlaps from case data */}
						{caseDetails?.evidence?.networkConnections?.contactOverlaps && caseDetails.evidence.networkConnections.contactOverlaps.length > 0 && (
							caseDetails.evidence.networkConnections.contactOverlaps.map((contact, index) => (
								<motion.div 
									key={index}
									className={`rounded-lg p-4 ${
										contact.riskLevel === 'Critical' ? 'bg-red-50 border border-red-200' :
										contact.riskLevel === 'High' ? 'bg-orange-50 border border-orange-200' :
										'bg-yellow-50 border border-yellow-200'
									}`}
									whileHover={{ scale: 1.02 }}
									transition={{ type: "spring", stiffness: 300 }}
								>
									<div className="flex justify-between items-center mb-3">
										<div className="flex items-center space-x-2">
											<div className={`w-10 h-10 rounded-full flex items-center justify-center ${
												contact.riskLevel === 'Critical' ? 'bg-red-100' :
												contact.riskLevel === 'High' ? 'bg-orange-100' :
												'bg-yellow-100'
											}`}>
												{contact.type === 'phone' ? '📞' : contact.type === 'email' ? '📧' : '🌐'}
											</div>
											<div>
												<span className={`font-medium ${
													contact.riskLevel === 'Critical' ? 'text-red-900' :
													contact.riskLevel === 'High' ? 'text-orange-900' :
													'text-yellow-900'
												}`}>Contact Overlap</span>
												<div className={`text-xs ${
													contact.riskLevel === 'Critical' ? 'text-red-700' :
													contact.riskLevel === 'High' ? 'text-orange-700' :
													'text-yellow-700'
												}`}>{contact.type.toUpperCase()}</div>
											</div>
										</div>
										<span className={`text-sm px-3 py-1 rounded-full font-medium ${
											contact.riskLevel === 'Critical' ? 'bg-red-100 text-red-800' :
											contact.riskLevel === 'High' ? 'bg-orange-100 text-orange-800' :
											'bg-yellow-100 text-yellow-800'
										}`}>
											🚨 {contact.riskLevel}
										</span>
									</div>
									<div className={`text-sm mb-2 ${
										contact.riskLevel === 'Critical' ? 'text-red-700' :
										contact.riskLevel === 'High' ? 'text-orange-700' :
										'text-yellow-700'
									}`}>
										<strong>Shared Contact:</strong>
										<br />• {contact.value}
										<br />• Used by: {contact.companies.join(', ')}
									</div>
								</motion.div>
							))
						)}
					</div>
				</motion.div>

				{/* Address Clustering */}
				<motion.div
					initial={{ opacity: 0, x: 20 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ delay: 0.4 }}
				>
					<div className="flex items-center space-x-2 mb-4">
						<MapPin className="w-5 h-5 text-green-600" />
						<h3 className="text-lg font-semibold text-gray-900">📍 Geographic Analysis</h3>
					</div>
					<div className="space-y-4">
						{addressClusters.map((cluster, index) => (
							<motion.div 
								key={index} 
								className={`border rounded-lg p-4 ${
									cluster.suspiciousLevel === "High" 
										? "bg-red-50 border-red-200" 
										: "bg-yellow-50 border-yellow-200"
								}`}
								whileHover={{ scale: 1.02 }}
								transition={{ type: "spring", stiffness: 300 }}
							>
								<div className="flex justify-between items-center mb-3">
									<div className="flex items-center space-x-2">
										<MapPin className={`w-5 h-5 ${
											cluster.suspiciousLevel === "High" ? "text-red-600" : "text-yellow-600"
										}`} />
										<span className="font-medium text-gray-900">{cluster.address}</span>
									</div>
									<span className={`text-sm px-3 py-1 rounded-full font-medium ${
										cluster.suspiciousLevel === "High" 
											? "bg-red-100 text-red-800" 
											: "bg-yellow-100 text-yellow-800"
									}`}>
										{cluster.suspiciousLevel === "High" ? "🚨" : "⚠️"} {cluster.suspiciousLevel} Risk
									</span>
								</div>
								<div className="text-sm text-gray-700 mb-3">
									<span className="font-medium">📋 {cluster.companies.length} companies co-located:</span>
								</div>
								<div className="flex flex-wrap gap-2">
									{cluster.companies.map((company, idx) => (
										<motion.span
											key={idx}
											whileHover={{ scale: 1.1 }}
											className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
												company === caseItem.winner 
													? "bg-red-100 text-red-800 border border-red-300" 
													: "bg-gray-100 text-gray-700 border border-gray-300"
											}`}
										>
											{company === caseItem.winner && "🏆 "}
											{company}
										</motion.span>
									))}
								</div>
								{cluster.suspiciousLevel === "High" && (
									<div className="mt-3 text-xs text-red-600 bg-red-100 p-2 rounded">
										🔍 Investigate: Multiple bidders sharing the same address suggests possible shell companies
									</div>
								)}
							</motion.div>
						))}
					</div>
				</motion.div>
			</div>

			{/* Contact Information Analysis */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.6 }}
			>
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center space-x-2">
						<Phone className="w-5 h-5 text-purple-600" />
						<h3 className="text-lg font-semibold text-gray-900">📞 Contact Information Analysis</h3>
					</div>
					<motion.div 
						className="flex items-center space-x-2 text-sm text-red-600"
						animate={{ opacity: [0.7, 1, 0.7] }}
						transition={{ repeat: Number.POSITIVE_INFINITY, duration: 2 }}
					>
						<AlertCircle className="w-4 h-4" />
						<span className="font-medium">Suspicious Patterns Detected</span>
					</motion.div>
				</div>
				<div className="bg-white border rounded-lg overflow-hidden shadow-sm">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Company</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
									<Phone className="w-4 h-4 inline mr-1" />Phone
								</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
									<Mail className="w-4 h-4 inline mr-1" />Email Domain
								</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Match Type</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Risk Level</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{/* Winner row */}
							<motion.tr 
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 0.8 }}
								className="hover:bg-gray-50"
							>
								<td className="px-4 py-3 text-sm font-medium text-gray-900">
									🏆 {caseItem.winner}
								</td>
								<td className="px-4 py-3 text-sm text-gray-700">
									{getCompanyDetails(caseItem.winner)?.phone || "N/A"}
								</td>
								<td className="px-4 py-3 text-sm text-gray-700">
									{getCompanyDetails(caseItem.winner)?.email?.split('@')[1] ? `@${getCompanyDetails(caseItem.winner)?.email?.split('@')[1]}` : "N/A"}
								</td>
								<td className="px-4 py-3 text-sm text-gray-700">-</td>
								<td className="px-4 py-3">
									<span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
										Primary
									</span>
								</td>
							</motion.tr>
							
							{/* Contact overlap rows */}
							{caseDetails?.evidence.networkConnections.contactOverlaps.map((contact, index) => {
								return contact.companies.map((company, compIndex) => {
									if (company === caseItem.winner) return null // Skip winner as it's already shown
									
									const isPhone = contact.type === 'phone'
									const isEmailDomain = contact.type === 'email_domain'
									const isCritical = contact.riskLevel === 'Critical'
									const isHigh = contact.riskLevel === 'High'
									
									return (
										<motion.tr 
											key={`${index}-${compIndex}`}
											initial={{ opacity: 0 }}
											animate={{ opacity: 1 }}
											transition={{ delay: 0.8 + (index + compIndex) * 0.2 }}
											className={`${isCritical ? 'bg-red-25 hover:bg-red-50' : 'hover:bg-orange-50'}`}
										>
											<td className="px-4 py-3 text-sm text-gray-700">🏢 {company}</td>
											<td className="px-4 py-3 text-sm text-gray-700">
												{isPhone ? (
													<div className={`${isCritical ? 'text-red-700 font-bold' : 'text-gray-700'}`}>
														📞 {contact.value}
														{isCritical && <div className="text-xs text-red-600">⚠️ IDENTICAL</div>}
													</div>
												) : (
													getCompanyDetails(company)?.phone || "N/A"
												)}
											</td>
											<td className="px-4 py-3 text-sm text-gray-700">
												{isEmailDomain ? (
													<div className={`${isHigh ? 'text-orange-700 font-medium' : 'text-gray-700'}`}>
														📧 {contact.value}
														{isHigh && <div className="text-xs text-orange-600">⚠️ SAME DOMAIN</div>}
													</div>
												) : (
													getCompanyDetails(company)?.email?.split('@')[1] ? `@${getCompanyDetails(company)?.email?.split('@')[1]}` : "N/A"
												)}
											</td>
											<td className="px-4 py-3 text-sm font-medium">
												{isPhone && isCritical && "🚨 Identical Phone"}
												{isEmailDomain && isHigh && "🟡 Same Email Domain"}
												{!isPhone && !isEmailDomain && "-"}
											</td>
											<td className="px-4 py-3">
												<span className={`px-2 py-1 rounded text-xs font-medium ${
													isCritical ? 'bg-red-100 text-red-800' : 
													isHigh ? 'bg-orange-100 text-orange-800' : 
													'bg-gray-100 text-gray-800'
												}`}>
													{isCritical && "🔴 Critical"}
													{isHigh && !isCritical && "🟠 High"}
													{!isCritical && !isHigh && "⚪ Low"}
												</span>
											</td>
										</motion.tr>
									)
								}).filter(Boolean)
							}).flat()}
							
							{/* Regular competitors without contact overlaps */}
							{caseDetails?.competitors
								.filter(competitor => !caseDetails.evidence.networkConnections.contactOverlaps
									.some(contact => contact.companies.includes(competitor.name)))
								.map((competitor, index) => (
								<motion.tr 
									key={`comp-${index}`}
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									transition={{ delay: 1.0 + index * 0.2 }}
									className="hover:bg-gray-50"
								>
									<td className="px-4 py-3 text-sm text-gray-700">🏢 {competitor.name}</td>
									<td className="px-4 py-3 text-sm text-gray-700">
										{getCompanyDetails(competitor.name)?.phone || "N/A"}
									</td>
									<td className="px-4 py-3 text-sm text-gray-700">
										{getCompanyDetails(competitor.name)?.email?.split('@')[1] ? `@${getCompanyDetails(competitor.name)?.email?.split('@')[1]}` : "N/A"}
									</td>
									<td className="px-4 py-3 text-sm text-gray-700">-</td>
									<td className="px-4 py-3">
										<span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-medium">
											⚪ Clean
										</span>
									</td>
								</motion.tr>
							))}
						</tbody>
					</table>
				</div>
				
				{/* Critical Finding Alert - Only show if there are critical contact overlaps */}
				{caseDetails?.evidence.networkConnections.contactOverlaps.some(contact => contact.riskLevel === 'Critical') && (
					<motion.div 
						className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4"
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						transition={{ delay: 1.4 }}
					>
						<div className="flex items-center space-x-3">
							<AlertCircle className="w-6 h-6 text-red-600 flex-shrink-0" />
							<div>
								<div className="font-semibold text-red-900">🚨 Critical Finding: Identical Contact Information</div>
								<div className="text-sm text-red-700 mt-1">
									{(() => {
										const criticalContact = caseDetails.evidence.networkConnections.contactOverlaps.find(c => c.riskLevel === 'Critical')
										if (criticalContact) {
											const companies = criticalContact.companies.join(' and ')
											const contactType = criticalContact.type === 'phone' ? 'phone number' : 'email domain'
											return `${companies} share the same ${contactType}, indicating potential coordination or shell company structure. This pattern strongly suggests bid rigging.`
										}
										return "Critical contact information overlap detected between companies."
									})()}
								</div>
								<div className="mt-2">
									<button 
										type="button"
										className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
									>
										🔍 Flag for Investigation
									</button>
								</div>
							</div>
						</div>
					</motion.div>
				)}
			</motion.div>
		</div>
	)
}