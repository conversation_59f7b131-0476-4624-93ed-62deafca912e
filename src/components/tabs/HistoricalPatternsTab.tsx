import type { ProcurementCase } from "../../types/procurement"
import { formatCurrency, formatDate } from "../../utils/riskUtils"
import { realisticProcurementDatabase } from "../../data/mockData"

interface HistoricalPatternsTabProps {
	caseItem: ProcurementCase
}

export default function HistoricalPatternsTab({ caseItem }: HistoricalPatternsTabProps) {
	// Get real case data from realistic database
	const allCases = realisticProcurementDatabase.procurementCases
	
	// Generate realistic bidding history from real cases involving this winner
	const biddingHistory: Array<{
		id: string;
		project: string;
		date: string;
		amount: number;
		bidders: number;
		winner: string;
		winRate: number;
		category: string;
	}> = []
	
	// Add all real cases where this company was involved (as winner or competitor)
	allCases.forEach(realCase => {
		const isWinner = realCase.winner === caseItem.winner
		const isCompetitor = realCase.competitors?.some(comp => comp.name === caseItem.winner)
		
		if (isWinner || isCompetitor) {
			const totalBidders = (realCase.competitors?.length || 0) + 1
			biddingHistory.push({
				id: realCase.id,
				project: realCase.title,
				date: realCase.timeline?.[0]?.date || "2024-01-01",
				amount: realCase.value,
				bidders: totalBidders,
				winner: realCase.winner,
				winRate: isWinner ? (1 / totalBidders) : 0,
				category: realCase.department.includes("IT") || realCase.department.includes("Technology") ? "Technology" :
						 realCase.department.includes("Health") || realCase.department.includes("Medical") ? "Healthcare" :
						 realCase.department.includes("Transport") || realCase.department.includes("Highway") ? "Infrastructure" :
						 realCase.department.includes("School") ? "Education" : "General Services"
			})
		}
	})
	
	// Sort by date
	biddingHistory.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

	// Calculate real performance metrics from actual case data
	const performanceMetrics: Array<{
		company: string;
		totalBids: number;
		wins: number;
		winRate: number;
		avgBidAmount: number;
		totalValue: number;
		categories: string[];
		riskLevel: string;
	}> = []
	const allCompanies = new Set<string>()
	
	// Collect all unique companies
	allCases.forEach(case_ => {
		allCompanies.add(case_.winner)
		case_.competitors?.forEach(comp => allCompanies.add(comp.name))
	})
	
	// Calculate metrics for each company
	Array.from(allCompanies).forEach(companyName => {
		const companyBids: typeof allCases = []
		let wins = 0
		let totalValue = 0
		const categories = new Set()
		
		allCases.forEach(case_ => {
			const isWinner = case_.winner === companyName
			const isCompetitor = case_.competitors?.some(comp => comp.name === companyName)
			
			if (isWinner || isCompetitor) {
				companyBids.push(case_)
				
				if (isWinner) {
					wins++
					totalValue += case_.value
				}
				
				// Categorize
				if (case_.department.includes("IT") || case_.department.includes("Technology")) {
					categories.add("Technology")
				} else if (case_.department.includes("Health")) {
					categories.add("Healthcare")
				} else if (case_.department.includes("Transport") || case_.department.includes("Highway")) {
					categories.add("Infrastructure")
				}
			}
		})
		
		if (companyBids.length > 0) {
			const winRate = (wins / companyBids.length) * 100
			const avgBidAmount = companyBids.reduce((sum, case_) => sum + case_.value, 0) / companyBids.length
			
			performanceMetrics.push({
				company: companyName,
				totalBids: companyBids.length,
				wins,
				winRate,
				avgBidAmount,
				totalValue,
				categories: Array.from(categories) as string[],
				riskLevel: winRate > 40 ? "High" : winRate > 25 ? "Medium" : "Low"
			})
		}
	})
	
	// Sort by win rate descending and limit to top competitors
	performanceMetrics.sort((a, b) => b.winRate - a.winRate)
	const topPerformers = performanceMetrics.slice(0, 4)

	// Generate contract patterns based on real data
	const contractPatterns = []
	const winnerMetrics = topPerformers.find(p => p.company === caseItem.winner)
	const winnerBids = biddingHistory.filter(bid => bid.winner === caseItem.winner)
	
	if (winnerMetrics && winnerBids.length > 0) {
		// Check for sequential wins
		if (winnerBids.length >= 2) {
			contractPatterns.push({
				pattern: "Multiple Contract Awards",
				description: `${caseItem.winner} has won ${winnerBids.length} contracts in the database`,
				riskLevel: winnerBids.length >= 3 ? "High" : "Medium",
				timespan: "Recent period",
				totalValue: winnerMetrics.totalValue
			})
		}
		
		// Check category concentration
		const categories = [...new Set(winnerBids.map(bid => bid.category))]
		if (categories.length === 1 && winnerBids.length >= 2) {
			contractPatterns.push({
				pattern: "Category Specialization",
				description: `All wins are in ${categories[0]} sector`,
				riskLevel: "Medium",
				timespan: "All cases",
				totalValue: winnerMetrics.totalValue
			})
		}
		
		// Check win rate
		if (winnerMetrics.winRate > 50) {
			contractPatterns.push({
				pattern: "High Win Rate",
				description: `Win rate of ${winnerMetrics.winRate.toFixed(1)}% is significantly above average`,
				riskLevel: "High",
				timespan: "All bidding history",
				totalValue: winnerMetrics.totalValue
			})
		}
	}
	
	// Check competition levels
	const avgBidders = biddingHistory.reduce((sum, bid) => sum + bid.bidders, 0) / biddingHistory.length
	if (avgBidders < 5) {
		contractPatterns.push({
			pattern: "Limited Competition",
			description: `Average of ${avgBidders.toFixed(1)} bidders per contract (below typical 5-7)`,
			riskLevel: "Medium",
			timespan: "All cases",
			totalValue: biddingHistory.reduce((sum, bid) => sum + bid.amount, 0)
		})
	}

	// Calculate bidding behavior from real data
	const biddingBehavior = []
	
	if (winnerMetrics) {
		// Win rate analysis
		const winRate = winnerMetrics.winRate
		biddingBehavior.push({
			metric: "Bid-to-Win Ratio",
			value: `${winRate.toFixed(1)}%`,
			benchmark: "15-25%",
			status: winRate > 40 ? "High Risk" : winRate > 30 ? "Concerning" : winRate > 20 ? "Elevated" : "Normal",
			description: winRate > 30 ? "Win rate significantly above industry average" : "Win rate within expected range"
		})
		
		// Competition analysis
		const avgCompetitors = biddingHistory.reduce((sum, bid) => sum + bid.bidders, 0) / biddingHistory.length
		biddingBehavior.push({
			metric: "Average Competition",
			value: `${avgCompetitors.toFixed(1)} bidders`,
			benchmark: "5-8 bidders",
			status: avgCompetitors < 4 ? "High Risk" : avgCompetitors < 5 ? "Concerning" : "Normal",
			description: avgCompetitors < 5 ? "Low competition may indicate market manipulation" : "Healthy competition levels"
		})
		
		// Value concentration
		const avgValue = winnerMetrics.avgBidAmount
		biddingBehavior.push({
			metric: "Average Contract Value",
			value: formatCurrency(avgValue),
			benchmark: "Varies by sector",
			status: winnerMetrics.totalValue > 5000000 ? "High Value" : "Standard",
			description: `Total value won: ${formatCurrency(winnerMetrics.totalValue)}`
		})
		
		// Risk assessment based on case data
		const riskScore = caseItem.riskScore
		biddingBehavior.push({
			metric: "Current Case Risk",
			value: `${riskScore}/100`,
			benchmark: "< 30 (Low Risk)",
			status: riskScore >= 70 ? "High Risk" : riskScore >= 40 ? "Medium Risk" : "Low Risk",
			description: riskScore >= 70 ? "Multiple red flags detected" : riskScore >= 40 ? "Some concerns identified" : "No major issues identified"
		})
	}

	const getPerformanceColor = (winRate: number) => {
		if (winRate > 25) return "text-red-600 bg-red-50"
		if (winRate > 15) return "text-orange-600 bg-orange-50"
		return "text-green-600 bg-green-50"
	}

	const getRiskColor = (level: string) => {
		switch (level) {
			case "High": return "text-red-600 bg-red-50"
			case "Medium": return "text-orange-600 bg-orange-50"
			case "Low": return "text-green-600 bg-green-50"
			default: return "text-gray-600 bg-gray-50"
		}
	}

	const getStatusColor = (status: string) => {
		switch (status) {
			case "High Risk":
			case "Suspicious": return "text-red-600 bg-red-50"
			case "Concerning": return "text-orange-600 bg-orange-50"
			case "Normal": return "text-green-600 bg-green-50"
			default: return "text-gray-600 bg-gray-50"
		}
	}

	return (
		<div className="p-6 space-y-8">
			{/* Company Bidding History */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">
					{caseItem.winner} - Bidding History
				</h3>
				<div className="bg-white border rounded-lg overflow-hidden">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Project</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
								<th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Value</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Bidders</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Result</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Win Rate</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{biddingHistory.map((bid, index) => (
								<tr key={index} className={bid.id === caseItem.id ? "bg-blue-50" : ""}>
									<td className="px-4 py-3 text-sm font-medium text-gray-900">
										{bid.project}
										{bid.id === caseItem.id && (
											<span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
												Current
											</span>
										)}
									</td>
									<td className="px-4 py-3 text-sm text-gray-700">{formatDate(bid.date)}</td>
									<td className="px-4 py-3 text-sm text-gray-900 text-right">
										{formatCurrency(bid.amount)}
									</td>
									<td className="px-4 py-3 text-sm text-gray-900 text-center">{bid.bidders}</td>
									<td className="px-4 py-3 text-center">
										{bid.winner === caseItem.winner ? (
											<span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
												Won
											</span>
										) : (
											<span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
												Lost
											</span>
										)}
									</td>
									<td className="px-4 py-3 text-center">
										<span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(bid.winRate * 100)}`}>
											{(bid.winRate * 100).toFixed(1)}%
										</span>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Performance Comparison */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Competitor Performance Analysis</h3>
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{topPerformers.map((company, index) => (
						<div key={index} className={`border rounded-lg p-4 ${getRiskColor(company.riskLevel)}`}>
							<div className="flex justify-between items-center mb-3">
								<h4 className="font-medium text-gray-900">{company.company}</h4>
								<span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(company.riskLevel)}`}>
									{company.riskLevel} Risk
								</span>
							</div>
							<div className="grid grid-cols-2 gap-4 text-sm">
								<div>
									<span className="text-gray-600">Total Bids:</span>
									<div className="font-semibold">{company.totalBids}</div>
								</div>
								<div>
									<span className="text-gray-600">Wins:</span>
									<div className="font-semibold">{company.wins}</div>
								</div>
								<div>
									<span className="text-gray-600">Win Rate:</span>
									<div className="font-semibold">{company.winRate.toFixed(1)}%</div>
								</div>
								<div>
									<span className="text-gray-600">Total Value:</span>
									<div className="font-semibold">{formatCurrency(company.totalValue)}</div>
								</div>
							</div>
							<div className="mt-3 text-xs text-gray-600">
								Categories: {company.categories.join(", ")}
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Contract Award Patterns */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Contract Award Patterns</h3>
				<div className="space-y-4">
					{contractPatterns.map((pattern, index) => (
						<div key={index} className={`border rounded-lg p-4 ${getRiskColor(pattern.riskLevel)}`}>
							<div className="flex justify-between items-start mb-2">
								<div>
									<h4 className="font-medium text-gray-900">{pattern.pattern}</h4>
									<p className="text-sm text-gray-600 mt-1">{pattern.description}</p>
								</div>
								<span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(pattern.riskLevel)}`}>
									{pattern.riskLevel}
								</span>
							</div>
							<div className="flex justify-between items-center text-sm">
								<span className="text-gray-600">Timespan: {pattern.timespan}</span>
								<span className="font-medium">Total Value: {formatCurrency(pattern.totalValue)}</span>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Bidding Behavior Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Bidding Behavior Analysis</h3>
				<div className="bg-white border rounded-lg overflow-hidden">
					<table className="min-w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Metric</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Current Value</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Industry Benchmark</th>
								<th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Assessment</th>
								<th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Analysis</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200">
							{biddingBehavior.map((behavior, index) => (
								<tr key={index}>
									<td className="px-4 py-3 text-sm font-medium text-gray-900">{behavior.metric}</td>
									<td className="px-4 py-3 text-sm text-gray-900 text-center font-medium">
										{behavior.value}
									</td>
									<td className="px-4 py-3 text-sm text-gray-600 text-center">
										{behavior.benchmark}
									</td>
									<td className="px-4 py-3 text-center">
										<span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(behavior.status)}`}>
											{behavior.status}
										</span>
									</td>
									<td className="px-4 py-3 text-sm text-gray-600">{behavior.description}</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Time Series Analysis */}
			<div>
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Win Rate Trend Analysis</h3>
				<div className="bg-white border rounded-lg p-6">
					<div className="relative h-48">
						<svg width="100%" height="100%" viewBox="0 0 100 80" className="absolute inset-0">
							{/* Grid lines */}
							{[20, 40, 60].map(y => (
								<line key={y} x1="10" y1={y} x2="95" y2={y} stroke="#e5e7eb" strokeWidth="0.2" />
							))}
							
							{/* Win rate line */}
							<polyline
								points="15,60 30,55 45,45 60,40 75,35 90,30"
								fill="none"
								stroke="#dc2626"
								strokeWidth="1"
							/>
							
							{/* Data points */}
							{[15, 30, 45, 60, 75, 90].map((x, index) => {
								const y = 60 - (index * 5)
								return (
									<circle key={index} cx={x} cy={y} r="1" fill="#dc2626" />
								)
							})}
							
							{/* Benchmark line */}
							<line x1="10" y1="50" x2="95" y2="50" stroke="#6b7280" strokeWidth="0.5" strokeDasharray="2,2" />
							<text x="85" y="47" fontSize="2" fill="#6b7280">Industry Avg (20%)</text>
						</svg>
						
						{/* Axis labels */}
						<div className="absolute bottom-2 left-4 text-xs text-gray-600">2023 Q1</div>
						<div className="absolute bottom-2 right-4 text-xs text-gray-600">2024 Q1</div>
						<div className="absolute left-2 top-4 text-xs text-gray-600">40%</div>
						<div className="absolute left-2 bottom-8 text-xs text-gray-600">10%</div>
					</div>
					
					{/* Dynamic trend analysis based on real data */}
					{winnerMetrics && winnerMetrics.winRate > 25 ? (
						<div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
							<div className="flex items-center space-x-2">
								<span className="text-red-600 text-lg">📈</span>
								<div>
									<div className="font-medium text-red-900">High Win Rate Detected</div>
									<div className="text-sm text-red-700">
										Current win rate of {winnerMetrics.winRate.toFixed(1)}% is above typical industry benchmarks of 15-25%
									</div>
								</div>
							</div>
						</div>
					) : (
						<div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
							<div className="flex items-center space-x-2">
								<span className="text-green-600 text-lg">✅</span>
								<div>
									<div className="font-medium text-green-900">Normal Performance Pattern</div>
									<div className="text-sm text-green-700">
										Win rate appears to be within expected industry ranges
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}