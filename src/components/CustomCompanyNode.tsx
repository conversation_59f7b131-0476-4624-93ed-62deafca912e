import { memo } from 'react'
import { Handle, Position } from '@xyflow/react'

interface CustomCompanyNodeProps {
	data: {
		label: string
		type: 'winner' | 'competitor' | 'person' | 'holding'
		riskLevel?: 'critical' | 'high' | 'medium' | 'low'
	}
}

function CustomCompanyNode({ data }: CustomCompanyNodeProps) {
	const { label, type, riskLevel } = data

	const getNodeStyle = () => {
		switch (type) {
			case 'winner':
				return {
					background: 'linear-gradient(135deg, #fee2e2 0%, #fecaca 100%)',
					border: '3px solid #dc2626',
					color: '#7f1d1d',
					boxShadow: '0 8px 32px rgba(220, 38, 38, 0.3)',
				}
			case 'competitor':
				return {
					background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
					border: '2px solid #d97706',
					color: '#92400e',
					boxShadow: '0 6px 24px rgba(217, 119, 6, 0.2)',
				}
			case 'person':
				return {
					background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',
					border: '2px solid #2563eb',
					color: '#1e40af',
					boxShadow: '0 6px 24px rgba(37, 99, 235, 0.2)',
				}
			case 'holding':
				return {
					background: 'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%)',
					border: '2px solid #7c3aed',
					color: '#5b21b6',
					boxShadow: '0 6px 24px rgba(124, 58, 237, 0.2)',
				}
			default:
				return {
					background: '#f8fafc',
					border: '2px solid #cbd5e1',
					color: '#475569',
				}
		}
	}

	const getIcon = () => {
		switch (type) {
			case 'winner': return '🏆'
			case 'competitor': return '🏢'
			case 'person': return '👤'
			case 'holding': return '🏛️'
			default: return '🔷'
		}
	}

	const getRiskBadge = () => {
		if (!riskLevel || riskLevel === 'low') return null
		
		const badges = {
			critical: { text: 'CRITICAL', color: 'bg-red-600 text-white', icon: '🚨' },
			high: { text: 'HIGH', color: 'bg-red-500 text-white', icon: '⚠️' },
			medium: { text: 'MEDIUM', color: 'bg-orange-500 text-white', icon: '⚡' }
		}
		
		const badge = badges[riskLevel as keyof typeof badges]
		if (!badge) return null
		
		return (
			<div className={`absolute -top-2 -right-2 px-2 py-1 rounded-full text-xs font-bold ${badge.color} shadow-lg z-10`}>
				{badge.icon} {badge.text}
			</div>
		)
	}

	return (
		<div className="relative">
			{getRiskBadge()}
			<div 
				className={`px-4 py-3 shadow-md rounded-lg border-2 transition-all hover:scale-105 hover:shadow-lg ${
					type === 'person' ? 'rounded-full min-w-[100px] min-h-[100px]' : 'rounded-lg min-w-[160px]'
				}`}
				style={getNodeStyle()}
			>
				<div className="flex items-center space-x-3">
					<div className={`flex justify-center items-center ${
						type === 'person' 
							? 'w-12 h-12 rounded-full bg-white bg-opacity-30' 
							: 'w-10 h-10 rounded-lg bg-white bg-opacity-30'
					}`}>
						<span className="text-2xl">{getIcon()}</span>
					</div>
					<div className="flex-1 min-w-0">
						<div className="font-bold text-sm leading-tight">
							{label.split('\n').map((line, index) => (
								<div key={index}>{line}</div>
							))}
						</div>
						<div className="text-xs opacity-75 mt-1 capitalize">
							{type}
						</div>
					</div>
				</div>
			</div>

			<Handle
				type="target"
				position={Position.Top}
				className="w-3 h-3 !bg-blue-500 border-2 border-white"
				style={{ top: -6 }}
			/>
			<Handle
				type="source"
				position={Position.Bottom}
				className="w-3 h-3 !bg-blue-500 border-2 border-white"
				style={{ bottom: -6 }}
			/>
		</div>
	)
}

export default memo(CustomCompanyNode)