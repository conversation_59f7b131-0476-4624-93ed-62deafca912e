import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Gauge, AlertTriangle, Shield, Activity, TrendingUp } from "lucide-react"
import { getRiskLevel, getRiskIcon } from "../utils/riskUtils"

interface RiskOverviewPanelProps {
	overallRiskScore: number
}

export default function RiskOverviewPanel({ overallRiskScore }: RiskOverviewPanelProps) {
	const [animatedScore, setAnimatedScore] = useState(0)
	const [isAnimating, setIsAnimating] = useState(true)
	const riskLevel = getRiskLevel(overallRiskScore)
	const riskIcon = getRiskIcon(overallRiskScore)

	// Animate score on mount
	useEffect(() => {
		const duration = 2000 // 2 seconds
		const steps = 60
		const increment = overallRiskScore / steps
		let currentStep = 0

		const timer = setInterval(() => {
			currentStep++
			setAnimatedScore(Math.min(currentStep * increment, overallRiskScore))
			
			if (currentStep >= steps) {
				clearInterval(timer)
				setIsAnimating(false)
			}
		}, duration / steps)

		return () => clearInterval(timer)
	}, [overallRiskScore])

	const getRiskMessage = () => {
		if (overallRiskScore >= 71) return "Multiple corruption indicators detected"
		if (overallRiskScore >= 31) return "Some irregularities found"
		return "Minimal concerns detected"
	}

	const getRiskRecommendation = () => {
		if (overallRiskScore >= 71) return "Immediate investigation required"
		if (overallRiskScore >= 31) return "Enhanced monitoring recommended"
		return "Continue standard oversight"
	}

	const getGaugeColor = () => {
		if (overallRiskScore >= 71) return "#dc2626"
		if (overallRiskScore >= 31) return "#d97706"
		return "#16a34a"
	}

	const circumference = 2 * Math.PI * 80
	const strokeDasharray = `${(animatedScore / 100) * circumference} ${circumference}`

	const riskThresholds = [
		{ range: "0-30", level: "Low Risk", icon: "✅", color: "green", bgColor: "bg-green-50", textColor: "text-green-700" },
		{ range: "31-70", level: "Medium Risk", icon: "⚠️", color: "yellow", bgColor: "bg-yellow-50", textColor: "text-yellow-700" },
		{ range: "71-100", level: "High Risk", icon: "🚨", color: "red", bgColor: "bg-red-50", textColor: "text-red-700" }
	]

	return (
		<motion.div 
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			className="bg-white rounded-lg shadow-lg p-6 border border-gray-100"
		>
			{/* Header */}
			<div className="flex items-center justify-between mb-6">
				<div className="flex items-center space-x-3">
					<motion.div
						initial={{ rotate: -180 }}
						animate={{ rotate: 0 }}
						transition={{ duration: 1, ease: "easeOut" }}
					>
						<Gauge className="w-6 h-6 text-blue-600" />
					</motion.div>
					<h2 className="text-xl font-bold text-gray-900">🔍 Corruption Risk Overview</h2>
				</div>
				<motion.div 
					className="flex items-center space-x-2"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 1 }}
				>
					<Activity className="w-4 h-4 text-green-500" />
					<span className="text-sm text-gray-600">Live Analysis</span>
				</motion.div>
			</div>

			{/* Main Risk Gauge */}
			<div className="flex items-center justify-center mb-8">
				<div className="relative">
					<motion.svg 
						width="240" 
						height="240" 
						className="transform -rotate-90"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ delay: 0.2 }}
					>
						{/* Background circle */}
						<circle
							cx="120"
							cy="120"
							r="80"
							stroke="#e5e7eb"
							strokeWidth="16"
							fill="none"
						/>
						
						{/* Animated progress circle */}
						<motion.circle
							cx="120"
							cy="120"
							r="80"
							stroke={getGaugeColor()}
							strokeWidth="16"
							fill="none"
							strokeLinecap="round"
							strokeDasharray={strokeDasharray}
							initial={{ strokeDasharray: "0 502.4" }}
							animate={{ strokeDasharray }}
							transition={{ duration: 2, ease: "easeOut" }}
							style={{
								filter: overallRiskScore >= 71 ? "drop-shadow(0 0 8px #dc262650)" : 
										overallRiskScore >= 31 ? "drop-shadow(0 0 8px #d9770650)" : 
										"drop-shadow(0 0 8px #16a34a50)"
							}}
						/>
					</motion.svg>
					
					{/* Center content */}
					<div className="absolute inset-0 flex items-center justify-center">
						<div className="text-center">
							<motion.div 
								className="text-5xl mb-2"
								initial={{ scale: 0 }}
								animate={{ scale: 1 }}
								transition={{ delay: 1.5, type: "spring", stiffness: 200 }}
							>
								{riskIcon}
							</motion.div>
							<motion.div 
								className="text-4xl font-bold text-gray-900"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								transition={{ delay: 1 }}
							>
								{Math.round(animatedScore)}
							</motion.div>
							<div className="text-sm text-gray-600 font-medium">Risk Score</div>
							{isAnimating && (
								<motion.div 
									className="text-xs text-blue-600 mt-1"
									animate={{ opacity: [0.5, 1, 0.5] }}
									transition={{ repeat: Infinity, duration: 1 }}
								>
									Analyzing...
								</motion.div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Risk Assessment */}
			<motion.div 
				className="text-center mb-6"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 2 }}
			>
				<AnimatePresence mode="wait">
					<motion.div
						key={riskLevel}
						initial={{ opacity: 0, scale: 0.8 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.8 }}
						className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
							riskLevel === "High" 
								? "bg-red-100 text-red-800 border border-red-200" 
								: riskLevel === "Medium" 
								? "bg-yellow-100 text-yellow-800 border border-yellow-200" 
								: "bg-green-100 text-green-800 border border-green-200"
						}`}
					>
						{riskLevel === "High" && <AlertTriangle className="w-4 h-4 mr-2" />}
						{riskLevel === "Medium" && <TrendingUp className="w-4 h-4 mr-2" />}
						{riskLevel === "Low" && <Shield className="w-4 h-4 mr-2" />}
						{riskLevel} Risk Level
					</motion.div>
				</AnimatePresence>
				<motion.p 
					className="mt-3 text-gray-600 font-medium"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 2.2 }}
				>
					{getRiskMessage()}
				</motion.p>
				<motion.p 
					className={`mt-1 text-sm font-semibold ${
						riskLevel === "High" ? "text-red-700" : 
						riskLevel === "Medium" ? "text-orange-700" : "text-green-700"
					}`}
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 2.4 }}
				>
					📋 {getRiskRecommendation()}
				</motion.p>
			</motion.div>

			{/* Risk Thresholds */}
			<motion.div 
				className="grid grid-cols-3 gap-4 text-center"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 2.6 }}
			>
				{riskThresholds.map((threshold, index) => (
					<motion.div
						key={threshold.range}
						initial={{ opacity: 0, scale: 0.8 }}
						animate={{ opacity: 1, scale: 1 }}
						transition={{ delay: 2.8 + index * 0.1 }}
						className={`p-4 rounded-lg border-2 transition-all duration-300 hover:shadow-md ${
							(threshold.color === "green" && riskLevel === "Low") ||
							(threshold.color === "yellow" && riskLevel === "Medium") ||
							(threshold.color === "red" && riskLevel === "High")
								? `${threshold.bgColor} border-current shadow-md scale-105`
								: `${threshold.bgColor} border-transparent`
						}`}
					>
						<div className="text-3xl mb-1">{threshold.icon}</div>
						<div className={`text-xl font-bold ${threshold.textColor}`}>
							{threshold.range}
						</div>
						<div className={`text-sm ${threshold.textColor} opacity-80`}>
							{threshold.level}
						</div>
					</motion.div>
				))}
			</motion.div>

			{/* Quick Actions */}
			<motion.div 
				className="mt-6 pt-6 border-t border-gray-200"
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ delay: 3 }}
			>
				<div className="flex items-center justify-between">
					<div className="text-sm text-gray-600">
						<span className="font-medium">Last scan:</span> {new Date().toLocaleTimeString()}
					</div>
					<div className="flex space-x-3">
						<motion.button 
							whileHover={{ scale: 1.05 }}
							whileTap={{ scale: 0.95 }}
							className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
						>
							🔄 Rescan
						</motion.button>
						<motion.button 
							whileHover={{ scale: 1.05 }}
							whileTap={{ scale: 0.95 }}
							className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm"
						>
							📊 Details
						</motion.button>
					</div>
				</div>
			</motion.div>
		</motion.div>
	)
}