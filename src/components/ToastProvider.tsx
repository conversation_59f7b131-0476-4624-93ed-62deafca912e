import { Toaster } from "react-hot-toast"

export default function ToastProvider() {
	return (
		<Toaster
			position="top-right"
			toastOptions={{
				duration: 4000,
				style: {
					background: '#ffffff',
					color: '#374151',
					border: '1px solid #e5e7eb',
					borderRadius: '8px',
					fontSize: '14px',
					padding: '12px 16px',
					boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
				},
				success: {
					duration: 3000,
					iconTheme: {
						primary: '#10b981',
						secondary: '#ffffff',
					},
					style: {
						border: '1px solid #10b981',
					},
				},
				error: {
					duration: 5000,
					iconTheme: {
						primary: '#ef4444',
						secondary: '#ffffff',
					},
					style: {
						border: '1px solid #ef4444',
					},
				},
				loading: {
					iconTheme: {
						primary: '#3b82f6',
						secondary: '#ffffff',
					},
					style: {
						border: '1px solid #3b82f6',
					},
				},
			}}
		/>
	)
}