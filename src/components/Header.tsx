import { Link } from "@tanstack/react-router"
import { useState } from "react"
import toast from "react-hot-toast"
import { dashboardStats, corruptionSummary } from "../data/mockData"

export default function Header() {
	const [searchQuery, setSearchQuery] = useState("")
	const [selectedPeriod, setSelectedPeriod] = useState("Current Quarter")

	const handleSearch = () => {
		if (searchQuery.trim()) {
			toast.success(`🔍 Searching for: "${searchQuery}"`, { duration: 2000 })
			// In a real app, this would trigger a search function
			console.log("Searching for:", searchQuery)
		} else {
			toast.error("Please enter a search term", { duration: 2000 })
		}
	}

	const handleExportReport = () => {
		toast.loading("📊 Generating comprehensive report...", { duration: 1000 })
		setTimeout(() => {
			const reportData = `Procurement Integrity Report
Generated: ${new Date().toLocaleString()}
Period: ${selectedPeriod}

SUMMARY STATISTICS:
- Total Cases Analyzed: ${dashboardStats.totalCases}
- High Risk Alerts: ${corruptionSummary.severityDistribution.Critical}
- Pending Reviews: ${dashboardStats.underReview}
- Clean Cases: ${dashboardStats.cleanCases} (${dashboardStats.cleanPercentage}%)
- Flagged Cases: ${dashboardStats.flaggedCases} (${dashboardStats.flaggedPercentage}%)

ALERT BREAKDOWN:
- Critical Violations: ${corruptionSummary.severityDistribution.Critical}
- Price Manipulation Cases: ${corruptionSummary.violationCounts["Price Manipulation"]}
- Company Collusion Incidents: ${corruptionSummary.violationCounts["Company Collusion"]}
- Shell Company Networks: ${corruptionSummary.violationCounts["Zombie Companies"]}

This report was generated by the Procurement Integrity Audit Dashboard.
For detailed analysis, please access the full dashboard.`

			const blob = new Blob([reportData], { type: 'text/plain' })
			const url = URL.createObjectURL(blob)
			const a = document.createElement('a')
			a.href = url
			a.download = `procurement-integrity-report-${new Date().toISOString().split('T')[0]}.txt`
			a.click()
			URL.revokeObjectURL(url)
			toast.success("📈 Report exported successfully!", { duration: 3000 })
		}, 1000)
	}

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleSearch()
		}
	}

	return (
		<header className="bg-white border-b border-gray-200 px-6 py-4">
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-6">
					<Link to="/" className="text-xl font-bold text-gray-900">
						🏛️ Procurement Integrity Audit Dashboard
					</Link>
					<div className="flex items-center space-x-2">
						<input
							type="text"
							placeholder="Search tenders, companies, projects..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							onKeyPress={handleKeyPress}
							className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-80"
						/>
						<button 
							type="button"
							onClick={handleSearch}
							className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
						>
							Search
						</button>
					</div>
				</div>

				<div className="flex items-center space-x-4">
					<select
						value={selectedPeriod}
						onChange={(e) => {
							setSelectedPeriod(e.target.value)
							toast(`📅 Period changed to: ${e.target.value}`, { duration: 2000 })
						}}
						className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<option>Current Quarter</option>
						<option>Last Quarter</option>
						<option>Current Year</option>
						<option>Last Year</option>
						<option>Custom Range</option>
					</select>

					<button 
						type="button"
						onClick={handleExportReport}
						className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
					>
						📊 Export Report
					</button>

					<div className="flex items-center space-x-2 text-sm text-gray-600">
						<span>Last updated:</span>
						<span className="font-medium">2 min ago</span>
					</div>
				</div>
			</div>

			<div className="mt-4 flex items-center space-x-6 text-sm">
				<div className="flex items-center space-x-2">
					<span className="font-medium">Total Cases:</span>
					<span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">{dashboardStats.totalCases}</span>
				</div>
				<div className="flex items-center space-x-2">
					<span className="font-medium">High Risk Alerts:</span>
					<span className="px-2 py-1 bg-red-100 text-red-800 rounded">{corruptionSummary.severityDistribution.Critical}</span>
				</div>
				<div className="flex items-center space-x-2">
					<span className="font-medium">Pending Reviews:</span>
					<span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">{dashboardStats.underReview}</span>
				</div>
				<div className="flex items-center space-x-2">
					<span className="font-medium">Processing Status:</span>
					<span className="text-green-600">● Active</span>
				</div>
			</div>
		</header>
	)
}
