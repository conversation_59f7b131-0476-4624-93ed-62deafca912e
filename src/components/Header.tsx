import { Link } from "@tanstack/react-router"
import { useEffect, useRef, useState } from "react"
import toast from "react-hot-toast"
import { useCaseContext } from "../contexts/CaseContext"
import { corruptionSummary, dashboardStats, mockAlerts } from "../data/mockData"
import type { Alert } from "../types/procurement"
import { formatDate } from "../utils/riskUtils"

export default function Header() {
	const [searchQuery, setSearchQuery] = useState("")
	const [selectedPeriod, setSelectedPeriod] = useState("Current Quarter")
	const [showAlerts, setShowAlerts] = useState(false)
	const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set())
	const alertsRef = useRef<HTMLDivElement>(null)
	const { onCaseSelect } = useCaseContext()

	// Filter out dismissed alerts
	const visibleAlerts = mockAlerts.filter(alert => !dismissedAlerts.has(alert.id))
	const criticalAlerts = visibleAlerts.filter(alert => alert.type === "Critical")
	const highPriorityAlerts = visibleAlerts.filter(alert => alert.type === "High Priority")
	const totalActiveAlerts = visibleAlerts.length

	// Close alerts popup when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (alertsRef.current && !alertsRef.current.contains(event.target as Node)) {
				setShowAlerts(false)
			}
		}

		document.addEventListener('mousedown', handleClickOutside)
		return () => {
			document.removeEventListener('mousedown', handleClickOutside)
		}
	}, [])

	const handleViewCase = (caseId?: string) => {
		if (!caseId) return
		setShowAlerts(false)
		toast.loading("📊 Loading detailed case analysis...", { duration: 1000 })

		// Call the parent component's case selection handler
		if (onCaseSelect) {
			onCaseSelect(caseId)
		}
	}

	const handleDismissAlert = (alertId: string) => {
		setDismissedAlerts(prev => new Set([...prev, alertId]))
		toast.success("✅ Alert dismissed", { duration: 1500 })
	}

	const getAlertIcon = (type: Alert["type"]) => {
		switch (type) {
			case "Critical": return "🚨"
			case "High Priority": return "⚠️"
			case "Trend": return "📊"
			default: return "ℹ️"
		}
	}

	const getAlertColor = (type: Alert["type"]) => {
		switch (type) {
			case "Critical": return "border-red-200 bg-red-50 text-red-800"
			case "High Priority": return "border-orange-200 bg-orange-50 text-orange-800"
			case "Trend": return "border-blue-200 bg-blue-50 text-blue-800"
			default: return "border-gray-200 bg-gray-50 text-gray-800"
		}
	}

	const handleSearch = () => {
		if (searchQuery.trim()) {
			toast.success(`🔍 Searching for: "${searchQuery}"`, { duration: 2000 })
			// In a real app, this would trigger a search function
			console.log("Searching for:", searchQuery)
		} else {
			toast.error("Please enter a search term", { duration: 2000 })
		}
	}

	const handleExportReport = () => {
		toast.loading("📊 Generating comprehensive report...", { duration: 1000 })
		setTimeout(() => {
			const reportData = `Procurement Integrity Report
Generated: ${new Date().toLocaleString()}
Period: ${selectedPeriod}

SUMMARY STATISTICS:
- Total Cases Analyzed: ${dashboardStats.totalCases}
- High Risk Alerts: ${corruptionSummary.severityDistribution.Critical}
- Pending Reviews: ${dashboardStats.underReview}
- Clean Cases: ${dashboardStats.cleanCases} (${dashboardStats.cleanPercentage}%)
- Flagged Cases: ${dashboardStats.flaggedCases} (${dashboardStats.flaggedPercentage}%)

ALERT BREAKDOWN:
- Critical Violations: ${corruptionSummary.severityDistribution.Critical}
- Price Manipulation Cases: ${corruptionSummary.violationCounts["Price Manipulation"]}
- Company Collusion Incidents: ${corruptionSummary.violationCounts["Company Collusion"]}
- Shell Company Networks: ${corruptionSummary.violationCounts["Zombie Companies"]}

This report was generated by the Procurement Integrity Audit Dashboard.
For detailed analysis, please access the full dashboard.`

			const blob = new Blob([reportData], { type: 'text/plain' })
			const url = URL.createObjectURL(blob)
			const a = document.createElement('a')
			a.href = url
			a.download = `procurement-integrity-report-${new Date().toISOString().split('T')[0]}.txt`
			a.click()
			URL.revokeObjectURL(url)
			toast.success("📈 Report exported successfully!", { duration: 3000 })
		}, 1000)
	}

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			handleSearch()
		}
	}

	return (
		<header className="bg-white border-b border-gray-200 px-6 py-4 relative">
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-6">
					<Link to="/" className="text-xl font-bold text-gray-900">
						🏛️ Procurement Integrity Audit Dashboard
					</Link>
					<div className="flex items-center space-x-2">
						<input
							type="text"
							placeholder="Search tenders, companies, projects..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							onKeyPress={handleKeyPress}
							className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-80"
						/>
						<button
							type="button"
							onClick={handleSearch}
							className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
						>
							Search
						</button>
					</div>
				</div>

				<div className="flex items-center space-x-4">
					{/* Priority Alerts Icon with Badge */}
					<div className="relative" ref={alertsRef}>
						<button
							type="button"
							onClick={() => setShowAlerts(!showAlerts)}
							className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
							title={`${totalActiveAlerts} active alerts`}
						>
							<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
							</svg>
							{totalActiveAlerts > 0 && (
								<span className={`absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 ${criticalAlerts.length > 0 ? 'bg-red-600' : 'bg-orange-500'} rounded-full`}>
									{totalActiveAlerts}
								</span>
							)}
						</button>

						{/* Alerts Popup */}
						{showAlerts && (
							<div className="absolute right-0 top-full mt-2 w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
								<div className="p-4 border-b border-gray-200">
									<h3 className="text-lg font-semibold text-gray-900">Priority Alerts</h3>
									<p className="text-sm text-gray-500">{totalActiveAlerts} active alerts</p>
								</div>
								<div className="p-2">
									{visibleAlerts.length === 0 ? (
										<div className="text-center py-8 text-gray-500">
											<div className="text-4xl mb-2">✅</div>
											<p>No active alerts</p>
										</div>
									) : (
										<div className="space-y-2">
											{visibleAlerts.slice(0, 10).map((alert) => (
												<div
													key={alert.id}
													className={`border rounded-lg p-3 ${getAlertColor(alert.type)}`}
												>
													<div className="flex items-start justify-between">
														<div className="flex items-start space-x-2">
															<div className="text-lg">
																{getAlertIcon(alert.type)}
															</div>
															<div className="flex-1 min-w-0">
																<div className="flex items-center space-x-2">
																	<span className="font-semibold text-xs uppercase tracking-wide">
																		{alert.type}
																	</span>
																	{alert.caseId && (
																		<span className="text-xs bg-white bg-opacity-50 px-2 py-1 rounded">
																			{alert.caseId}
																		</span>
																	)}
																</div>
																<h4 className="font-medium text-sm mt-1 truncate">
																	{alert.title}
																</h4>
																<p className="text-xs mt-1 opacity-90 line-clamp-2">
																	{alert.description}
																</p>
																<div className="text-xs mt-1 opacity-75">
																	{formatDate(alert.timestamp)}
																</div>
															</div>
														</div>
														<div className="flex flex-col space-y-1 ml-2">
															{alert.caseId && (
																<button
																	type="button"
																	onClick={() => handleViewCase(alert.caseId)}
																	className="text-xs px-2 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded transition-colors whitespace-nowrap"
																>
																	View Case
																</button>
															)}
															<button
																type="button"
																onClick={() => handleDismissAlert(alert.id)}
																className="text-xs px-2 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded transition-colors"
															>
																Dismiss
															</button>
														</div>
													</div>
												</div>
											))}
											{visibleAlerts.length > 10 && (
												<div className="text-center py-2 text-sm text-gray-500">
													... and {visibleAlerts.length - 10} more alerts
												</div>
											)}
										</div>
									)}
								</div>
							</div>
						)}
					</div>

					{/* Quick Actions */}
					<button
						type="button"
						onClick={handleExportReport}
						className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
						title="Export comprehensive report"
					>
						📊 Export
					</button>

					{/* System Status */}
					<div className="flex items-center space-x-2 text-sm">
						<div className="flex items-center space-x-1">
							<span className="w-2 h-2 bg-green-500 rounded-full" />
							<span className="text-gray-600">System Active</span>
						</div>
					</div>
				</div>
			</div>

			{/* Enhanced Stats Bar */}
			<div className="mt-4 flex items-center justify-between">
				<div className="flex items-center space-x-6 text-sm">
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">Total Cases:</span>
						<span className="px-2 py-1 bg-blue-100 text-blue-800 rounded font-semibold">{dashboardStats.totalCases}</span>
					</div>
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">Critical Alerts:</span>
						<span className="px-2 py-1 bg-red-100 text-red-800 rounded font-semibold">{corruptionSummary.severityDistribution.Critical}</span>
					</div>
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">Under Review:</span>
						<span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded font-semibold">{dashboardStats.underReview}</span>
					</div>
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">Clean Cases:</span>
						<span className="px-2 py-1 bg-green-100 text-green-800 rounded font-semibold">{dashboardStats.cleanCases}</span>
					</div>
				</div>

				<div className="flex items-center space-x-4 text-sm">
					{/* Risk Level Indicator */}
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">Risk Level:</span>
						<div className={`px-3 py-1 rounded-full text-xs font-semibold ${
							corruptionSummary.severityDistribution.Critical > 20
								? 'bg-red-100 text-red-800'
								: corruptionSummary.severityDistribution.Critical > 10
								? 'bg-yellow-100 text-yellow-800'
								: 'bg-green-100 text-green-800'
						}`}>
							{corruptionSummary.severityDistribution.Critical > 20 ? 'HIGH' :
							 corruptionSummary.severityDistribution.Critical > 10 ? 'MEDIUM' : 'LOW'}
						</div>
					</div>

					{/* System Health */}
					<div className="flex items-center space-x-2">
						<span className="font-medium text-gray-700">System:</span>
						<div className="flex items-center space-x-1">
							<span className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
							<span className="text-green-600 font-medium">Active</span>
						</div>
					</div>

					{/* Last Update */}
					<div className="flex items-center space-x-2 text-gray-500">
						<span>Updated:</span>
						<span className="font-medium">2 min ago</span>
					</div>
				</div>
			</div>
		</header>
	)
}
