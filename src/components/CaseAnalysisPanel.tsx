import { useState } from "react"
import type { ProcurementCase } from "../types/procurement"
import { formatCurrency, formatDate, getRiskIcon, getRiskColor } from "../utils/riskUtils"
import CompanyRelationshipsTab from "./tabs/CompanyRelationshipsTab"
import PriceAnalysisTab from "./tabs/PriceAnalysisTab"
import DocumentAnalysisTab from "./tabs/DocumentAnalysisTab"
import HistoricalPatternsTab from "./tabs/HistoricalPatternsTab"

interface CaseAnalysisPanelProps {
	caseItem: ProcurementCase
	onClose: () => void
}

type TabType = "relationships" | "price" | "documents" | "patterns"

export default function CaseAnalysisPanel({ caseItem, onClose }: CaseAnalysisPanelProps) {
	const [activeTab, setActiveTab] = useState<TabType>("relationships")

	const tabs = [
		{ id: "relationships", label: "Company Relationships", icon: "🏢" },
		{ id: "price", label: "Price Analysis", icon: "💰" },
		{ id: "documents", label: "Document Analysis", icon: "📄" },
		{ id: "patterns", label: "Historical Patterns", icon: "📊" },
	] as const

	const renderTabContent = () => {
		switch (activeTab) {
			case "relationships":
				return <CompanyRelationshipsTab caseItem={caseItem} />
			case "price":
				return <PriceAnalysisTab caseItem={caseItem} />
			case "documents":
				return <DocumentAnalysisTab caseItem={caseItem} />
			case "patterns":
				return <HistoricalPatternsTab caseItem={caseItem} />
			default:
				return null
		}
	}

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[95vh] overflow-hidden m-4">
				{/* Header */}
				<div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
					<div className="flex justify-between items-start">
						<div className="flex-1">
							<div className="flex items-center space-x-4">
								<h2 className="text-2xl font-bold text-gray-900">
									{caseItem.projectName}
								</h2>
								<span className="text-lg text-gray-600">({caseItem.id})</span>
								<div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRiskColor(caseItem.riskScore)}`}>
									{getRiskIcon(caseItem.riskScore)} Risk Score: {caseItem.riskScore}
								</div>
							</div>
							<div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
								<div>
									<span className="font-medium text-gray-600">Budget:</span>
									<div className="text-lg font-semibold">{formatCurrency(caseItem.amount)}</div>
								</div>
								<div>
									<span className="font-medium text-gray-600">Winner:</span>
									<div className="font-semibold">{caseItem.winner}</div>
								</div>
								<div>
									<span className="font-medium text-gray-600">Bidders:</span>
									<div className="font-semibold">{caseItem.bidders}</div>
								</div>
								<div>
									<span className="font-medium text-gray-600">Agency:</span>
									<div className="font-semibold">{caseItem.agency}</div>
								</div>
							</div>
						</div>
						<button
							onClick={onClose}
							className="text-gray-400 hover:text-gray-600 text-3xl font-light"
						>
							×
						</button>
					</div>
				</div>

				{/* Violation Summary */}
				{caseItem.topViolations.length > 0 && (
					<div className="px-6 py-3 bg-red-50 border-b border-red-100">
						<div className="flex items-center space-x-2">
							<span className="text-red-600 font-medium">Detected Violations:</span>
							<div className="flex flex-wrap gap-2">
								{caseItem.topViolations.map((violation, index) => (
									<span
										key={index}
										className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium"
									>
										{violation.type} ({violation.severity})
									</span>
								))}
							</div>
						</div>
					</div>
				)}

				{/* Tab Navigation */}
				<div className="border-b border-gray-200">
					<nav className="flex space-x-8 px-6">
						{tabs.map((tab) => (
							<button
								key={tab.id}
								onClick={() => setActiveTab(tab.id)}
								className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
									activeTab === tab.id
										? "border-blue-500 text-blue-600"
										: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
								}`}
							>
								<span className="mr-2">{tab.icon}</span>
								{tab.label}
							</button>
						))}
					</nav>
				</div>

				{/* Tab Content */}
				<div className="flex-1 overflow-y-auto max-h-[calc(95vh-300px)]">
					{renderTabContent()}
				</div>

				{/* Footer */}
				<div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
					<div className="flex justify-between items-center">
						<div className="flex items-center space-x-4 text-sm text-gray-600">
							<span>Posted: {formatDate(caseItem.datePosted)}</span>
							<span>Deadline: {formatDate(caseItem.deadline)}</span>
							<span>Category: {caseItem.category}</span>
						</div>
						<div className="flex space-x-3">
							<button className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
								🚩 Flag for Investigation
							</button>
							<button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
								📊 Generate Report
							</button>
							<button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
								📋 Add Notes
							</button>
							<button
								onClick={onClose}
								className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}