import type { DashboardStats } from "../types/procurement"
import { formatPercentage } from "../utils/riskUtils"

interface QuickStatsCardsProps {
	stats: DashboardStats
}

export default function QuickStatsCards({ stats }: QuickStatsCardsProps) {
	const cards = [
		{
			title: "Total Cases",
			value: stats.totalCases.toLocaleString(),
			subtitle: "All processed",
			bgColor: "bg-blue-50",
			textColor: "text-blue-900",
			borderColor: "border-blue-200",
		},
		{
			title: "Clean Cases",
			value: stats.cleanCases.toLocaleString(),
			subtitle: `(${formatPercentage(stats.cleanPercentage)})`,
			bgColor: "bg-green-50",
			textColor: "text-green-900",
			borderColor: "border-green-200",
		},
		{
			title: "Flagged Cases",
			value: stats.flaggedCases.toLocaleString(),
			subtitle: `(${formatPercentage(stats.flaggedPercentage)})`,
			bgColor: "bg-red-50",
			textColor: "text-red-900",
			borderColor: "border-red-200",
		},
		{
			title: "Under Review",
			value: stats.underReview.toLocaleString(),
			subtitle: `(${formatPercentage(stats.reviewPercentage)})`,
			bgColor: "bg-yellow-50",
			textColor: "text-yellow-900",
			borderColor: "border-yellow-200",
		},
	]

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{cards.map((card, index) => (
				<div
					key={index}
					className={`${card.bgColor} ${card.borderColor} border rounded-lg p-6 hover:shadow-md transition-shadow`}
				>
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm font-medium text-gray-600">{card.title}</p>
							<p className={`text-3xl font-bold ${card.textColor} mt-2`}>
								{card.value}
							</p>
							<p className="text-sm text-gray-500 mt-1">{card.subtitle}</p>
						</div>
						<div className={`w-12 h-12 ${card.bgColor} rounded-full flex items-center justify-center`}>
							{index === 0 && <span className="text-2xl">📊</span>}
							{index === 1 && <span className="text-2xl">✅</span>}
							{index === 2 && <span className="text-2xl">🚩</span>}
							{index === 3 && <span className="text-2xl">🔍</span>}
						</div>
					</div>
				</div>
			))}
		</div>
	)
}