import type {
	ProcurementCase,
	DashboardStats,
	CorruptionSummary,
	Alert,
	CompanyRelationship,
} from "../types/procurement"
import realisticDatabase from "./realisticDatabase.json"

// Transform the realistic database into our component format
export const mockCases: ProcurementCase[] = realisticDatabase.procurementCases.map(case_ => ({
	id: case_.id,
	projectName: case_.title,
	riskScore: case_.riskScore,
	topViolations: case_.violations.map(violation => ({
		type: violation as any,
		severity: case_.riskScore >= 80 ? "Critical" : case_.riskScore >= 60 ? "High" : case_.riskScore >= 30 ? "Medium" : "Low",
		description: case_.evidence.bidManipulation.suspiciousPatterns[0] || "Standard violation pattern"
	})),
	bidders: (case_.competitors?.length || 0) + 1, // +1 for the winner
	winner: case_.winner,
	amount: case_.value,
	status: case_.status as any,
	agency: case_.department,
	datePosted: case_.timeline[0]?.date || "2024-01-01",
	deadline: case_.timeline[case_.timeline.length - 1]?.date || "2024-12-31",
	category: case_.department.includes("IT") || case_.department.includes("Technology") ? "Technology" :
		case_.department.includes("Health") || case_.department.includes("Medical") ? "Healthcare" :
		case_.department.includes("Transport") || case_.department.includes("Highway") ? "Infrastructure" :
		case_.department.includes("School") || case_.department.includes("Library") ? "Education" :
		case_.department.includes("Police") || case_.department.includes("Emergency") ? "Public Safety" :
		case_.department.includes("Water") || case_.department.includes("Environment") ? "Environment" :
		case_.department.includes("Parks") || case_.department.includes("Recreation") ? "Recreation" :
		"General Services"
}))

// Calculate realistic dashboard statistics based on actual case data
const actualCases = realisticDatabase.procurementCases
const totalCases = actualCases.length

// Count cases by risk level
const highRiskCases = actualCases.filter(c => c.riskScore >= 70).length
const mediumRiskCases = actualCases.filter(c => c.riskScore >= 40 && c.riskScore < 70).length
const lowRiskCases = actualCases.filter(c => c.riskScore < 40).length

// Calculate status distribution
const underInvestigation = actualCases.filter(c => c.status === "Under Investigation").length

const cleanCases = lowRiskCases + Math.floor(mediumRiskCases * 0.6) // Low risk + some medium risk
const flaggedCases = highRiskCases + Math.ceil(mediumRiskCases * 0.4) // High risk + remaining medium risk  
const underReview = underInvestigation

export const dashboardStats: DashboardStats = {
	totalCases,
	cleanCases,
	flaggedCases,
	underReview,
	cleanPercentage: Math.round((cleanCases / totalCases) * 100),
	flaggedPercentage: Math.round((flaggedCases / totalCases) * 100),
	reviewPercentage: Math.round((underReview / totalCases) * 100),
}

// Calculate corruption summary from actual case data
const violationCounts = actualCases.reduce((acc, case_) => {
	case_.violations.forEach(violation => {
		acc[violation] = (acc[violation] || 0) + 1
	})
	return acc
}, {} as Record<string, number>)

const severityDistribution = actualCases.reduce((acc, case_) => {
	const severity = case_.riskScore >= 80 ? "Critical" : 
					case_.riskScore >= 60 ? "High" : 
					case_.riskScore >= 30 ? "Medium" : "Low"
	acc[severity] = (acc[severity] || 0) + 1
	return acc
}, { Critical: 0, High: 0, Medium: 0, Low: 0 })

export const corruptionSummary: CorruptionSummary = {
	violationCounts: {
		"Company Collusion": violationCounts["Company Collusion"] || 0,
		"Price Manipulation": violationCounts["Price Manipulation"] || 0,
		"Specification Rigging": violationCounts["Specification Rigging"] || 0,
		"Document Similarity": violationCounts["Document Similarity"] || 0,
		"Zombie Companies": violationCounts["Zombie Companies"] || 0,
		"Repeated Winners": violationCounts["Repeated Winners"] || 0,
	},
	severityDistribution,
}

// Generate comprehensive alerts matching our statistics
export const mockAlerts: Alert[] = [
	// Original realistic alerts
	...realisticDatabase.alerts.map(alert => ({
		id: alert.id,
		type: alert.type as any,
		title: alert.title,
		description: alert.description,
		caseId: alert.caseId,
		timestamp: alert.timestamp,
	})),
	// Additional high-priority alerts to reach 23 critical
	{
		id: "ALERT-005",
		type: "Critical" as any,
		title: "Massive Bid Coordination Ring",
		description: "15 companies in construction sector showing identical bid patterns across 8 contracts.",
		timestamp: "2024-04-28T11:20:00Z",
	},
	{
		id: "ALERT-006", 
		type: "Critical" as any,
		title: "Shell Company Network",
		description: "Healthcare procurement showing 5 companies with same registered address and directors.",
		timestamp: "2024-04-25T09:15:00Z",
	},
	{
		id: "ALERT-007",
		type: "Critical" as any,
		title: "Price Manipulation Scheme",
		description: "Technology contracts showing 40% price inflation above market rates.",
		timestamp: "2024-04-22T14:30:00Z",
	},
	{
		id: "ALERT-008",
		type: "High Priority" as any,
		title: "Repeated Winner Pattern",
		description: "Same company won 12 consecutive similar contracts worth $45M.",
		timestamp: "2024-04-20T16:45:00Z",
	},
	{
		id: "ALERT-009",
		type: "Critical" as any,
		title: "Document Forgery Suspected",
		description: "Multiple bids contain identical technical specifications from different vendors.",
		timestamp: "2024-04-18T13:20:00Z",
	},
	{
		id: "ALERT-010",
		type: "High Priority" as any,
		title: "Unusual Bid Withdrawal Pattern",
		description: "Systematic bid withdrawals allowing specific company to win at higher prices.",
		timestamp: "2024-04-15T10:10:00Z",
	},
	{
		id: "ALERT-011",
		type: "Critical" as any,
		title: "Cross-Sector Collusion",
		description: "IT and construction companies sharing directors across multiple high-value contracts.",
		timestamp: "2024-04-12T08:30:00Z",
	},
	{
		id: "ALERT-012",
		type: "Trend" as any,
		title: "Rising Corruption Indicators",
		description: "30% increase in suspicious bidding patterns this quarter across all sectors.",
		timestamp: "2024-04-10T07:00:00Z",
	}
]

// Generate company relationships from the realistic database
export const mockCompanyRelationships: CompanyRelationship[] = [
	// High-risk relationships from T-2024-001
	{
		companyA: "TechNova Systems Inc",
		companyB: "Digital Solutions Corp",
		relationshipType: "Shared Directors",
		strength: 0.9,
	},
	{
		companyA: "TechNova Systems Inc",
		companyB: "Digital Solutions Corp",
		relationshipType: "Shared Address",
		strength: 1.0,
	},
	{
		companyA: "TechNova Systems Inc",
		companyB: "Digital Solutions Corp",
		relationshipType: "Shared Contact",
		strength: 1.0,
	},
	// Critical relationships from T-2024-005 (Water Treatment)
	{
		companyA: "AquaTech Industries",
		companyB: "Water Solutions LLC",
		relationshipType: "Shared Directors",
		strength: 1.0,
	},
	{
		companyA: "AquaTech Industries",
		companyB: "H2O Systems Corp",
		relationshipType: "Shared Directors",
		strength: 1.0,
	},
	{
		companyA: "Water Solutions LLC",
		companyB: "H2O Systems Corp",
		relationshipType: "Shared Directors",
		strength: 1.0,
	},
	{
		companyA: "AquaTech Industries",
		companyB: "Water Solutions LLC",
		relationshipType: "Shared Address",
		strength: 1.0,
	},
	{
		companyA: "AquaTech Industries",
		companyB: "H2O Systems Corp",
		relationshipType: "Shared Address",
		strength: 1.0,
	},
	{
		companyA: "Water Solutions LLC",
		companyB: "H2O Systems Corp",
		relationshipType: "Shared Address",
		strength: 1.0,
	},
	// Medium-risk relationships from T-2024-003
	{
		companyA: "MediCore Technologies",
		companyB: "Health Systems Pro",
		relationshipType: "Shared Contact",
		strength: 0.6,
	},
	// Communication system relationships from T-2024-009
	{
		companyA: "CommNet Technologies",
		companyB: "Signal Systems Inc",
		relationshipType: "Shared Directors",
		strength: 0.7,
	},
	{
		companyA: "CommNet Technologies",
		companyB: "Radio Wave Corp",
		relationshipType: "Shared Contact",
		strength: 0.5,
	},
]

// Export realistic database for detailed analysis
export const realisticProcurementDatabase = realisticDatabase

// Helper function to get detailed case information
export const getCaseDetails = (caseId: string) => {
	return realisticDatabase.procurementCases.find(case_ => case_.id === caseId)
}

// Helper function to get company information
export const getCompanyDetails = (companyName: string) => {
	return realisticDatabase.companies[companyName as keyof typeof realisticDatabase.companies]
}