{"name": "procurement-analysis", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run", "format": "biome format", "lint": "biome lint", "check": "biome check"}, "dependencies": {"@t3-oss/env-core": "^0.12.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/router-plugin": "^1.121.2", "@xyflow/react": "^12.8.2", "framer-motion": "^12.23.9", "lucide-react": "^0.526.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.0", "tailwindcss": "^4.0.6", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}