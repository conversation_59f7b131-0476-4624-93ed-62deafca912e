# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

This project supports both npm and Bun package managers. Core development commands:

- `npm install` or `bun install` - Install dependencies
- `npm run start` or `npm run dev` - Start development server on port 3000
- `npm run build` - Build for production (runs `vite build && tsc`)
- `npm run test` - Run tests with Vitest
- `npm run lint` - Lint code with Biome
- `npm run format` - Format code with Biome  
- `npm run check` - Run Biome check (lint + format)

## Project Overview

This is a **Government Procurement Audit Dashboard** - a sophisticated React application designed for auditors and procurement oversight personnel to identify potential corruption risks in government tenders and bids.

### Core Stack
- **React 19** with TypeScript
- **TanStack Router** for file-based routing with automatic code splitting
- **Vite** as the build tool and dev server
- **Tailwind CSS v4** for styling
- **Biome** for linting and formatting
- **Vitest** with js<PERSON> for testing

## Application Architecture

### Dashboard Components Structure
The main dashboard (`src/routes/index.tsx`) consists of several key components:

1. **Header** (`src/components/Header.tsx`)
   - Search functionality for tenders, companies, projects
   - Time period selector and export functions
   - Real-time status indicators

2. **Risk Overview Panel** (`src/components/RiskOverviewPanel.tsx`)
   - Interactive corruption risk gauge with color-coded zones (Green 0-30, Yellow 31-70, Red 71-100)
   - Dynamic risk level assessment

3. **Quick Stats Cards** (`src/components/QuickStatsCards.tsx`)
   - Total cases, clean cases, flagged cases, under review metrics
   - Percentage breakdowns with visual indicators

4. **Corruption Detection Summary** (`src/components/CorruptionDetectionSummary.tsx`)
   - Bar charts showing violation type frequency
   - Pie chart for severity distribution

5. **Case Investigation Table** (`src/components/CaseInvestigationTable.tsx`)
   - Sortable and filterable table of all procurement cases
   - Color-coded risk levels and interactive case selection

6. **Alert System** (`src/components/AlertSystem.tsx`)
   - Priority alerts for critical, high priority, and trend notifications

### Advanced Case Analysis
When a case is selected, the **Case Analysis Panel** (`src/components/CaseAnalysisPanel.tsx`) opens with four detailed analysis tabs:

#### Tab 1: Company Relationships (`src/components/tabs/CompanyRelationshipsTab.tsx`)
- Network visualization of company connections
- Shared directors and shareholders analysis
- Address clustering detection
- Contact information overlap analysis

#### Tab 2: Price Analysis (`src/components/tabs/PriceAnalysisTab.tsx`)
- Bid clustering detection and price pattern analysis
- Historical price trends and market rate comparisons
- Cost breakdown variance analysis
- Suspicious pricing indicators

#### Tab 3: Document Analysis (`src/components/tabs/DocumentAnalysisTab.tsx`)
- Document similarity heatmaps between proposals
- Specification rigging detection
- Timeline analysis of document submissions
- Content overlap percentage analysis

#### Tab 4: Historical Patterns (`src/components/tabs/HistoricalPatternsTab.tsx`)
- Company bidding history and win rate trends
- Performance metrics comparison
- Contract award pattern detection
- Bidding behavior analysis

### Data Management
- **Types** (`src/types/procurement.ts`) - Comprehensive TypeScript interfaces for all procurement data
- **Mock Data** (`src/data/mockData.ts`) - Realistic sample data for development and testing
- **Utilities** (`src/utils/riskUtils.ts`) - Helper functions for risk calculations, formatting, and color coding

### Key Features
- **Traffic Light Risk System**: Visual risk assessment with immediate color coding
- **Interactive Drill-down**: Click any case for detailed forensic analysis
- **Pattern Recognition**: Automated detection of corruption indicators including:
  - Company Collusion
  - Price Manipulation  
  - Specification Rigging
  - Document Similarity
  - Zombie Companies
  - Repeated Winners
- **Professional UI**: Clean, audit-focused interface optimized for investigative workflows
- **Real-time Alerts**: Priority-based notification system for immediate attention items

### Environment Variables
Environment variables are type-safe using @t3-oss/env-core:
- Server variables: `SERVER_URL` (optional)
- Client variables: Must be prefixed with `VITE_` (e.g., `VITE_APP_TITLE`)
- Import with: `import { env } from "@/env"`

### Code Style & Best Practices
- Biome configuration uses tab indentation and double quotes
- Strict TypeScript with comprehensive type safety
- Path alias: `@/*` maps to `./src/*`
- Component-based architecture with clear separation of concerns
- Consistent color schemes for risk levels (Red/Yellow/Green)
- Accessibility-focused design patterns

### Testing Strategy
- Vitest with jsdom environment for component testing
- Testing Library React for user interaction testing
- Type-safe mock data for reliable test scenarios